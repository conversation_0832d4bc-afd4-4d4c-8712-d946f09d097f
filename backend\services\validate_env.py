"""
Environment Variable Validation Script
Validates that all required environment variables are properly configured
Focuses on COVE_EMAIL and other critical configuration variables
"""

import os
import logging
import sys
from typing import Dict, List, Tuple, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnvironmentValidator:
    """Validates environment variables for COVE integration"""
    
    def __init__(self):
        self.validation_results = {}
        self.required_vars = {
            "COVE_EMAIL": {
                "expected": "<EMAIL>",
                "description": "COVE user email for authentication (Cloudflare forwarding)",
                "critical": True
            },
            "COVE_PASSWORD": {
                "expected": "CoveSecure123!",
                "description": "COVE user password for authentication",
                "critical": True
            },
            "AIGENCY_URL": {
                "expected": "http://localhost:3000",
                "description": "The AIgency backend URL",
                "critical": True
            },
            "REDIS_URL": {
                "expected": "redis://localhost:6379",
                "description": "Redis connection URL",
                "critical": False
            },
            "COVE_HOST": {
                "expected": "0.0.0.0",
                "description": "COVE service host",
                "critical": False
            },
            "COVE_PORT": {
                "expected": "8000",
                "description": "COVE service port",
                "critical": False
            },
            "LOG_LEVEL": {
                "expected": "INFO",
                "description": "Logging level",
                "critical": False
            }
        }
    
    def validate_all_variables(self) -> bool:
        """Validate all environment variables"""
        logger.info("🔍 Starting Environment Variable Validation...")
        logger.info(f"{'='*60}")
        
        all_valid = True
        critical_failures = 0
        
        for var_name, var_config in self.required_vars.items():
            result = self.validate_variable(var_name, var_config)
            self.validation_results[var_name] = result
            
            if not result["valid"]:
                all_valid = False
                if var_config["critical"]:
                    critical_failures += 1
        
        # Generate report
        self.generate_validation_report()
        
        # Return overall status
        if critical_failures > 0:
            logger.error(f"❌ {critical_failures} critical environment variable(s) failed validation")
            return False
        elif not all_valid:
            logger.warning("⚠️ Some non-critical environment variables need attention")
            return True
        else:
            logger.info("✅ All environment variables are properly configured")
            return True
    
    def validate_variable(self, var_name: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate a single environment variable"""
        current_value = os.getenv(var_name)
        expected_value = config["expected"]
        is_critical = config["critical"]
        
        result = {
            "variable": var_name,
            "current_value": current_value,
            "expected_value": expected_value,
            "description": config["description"],
            "critical": is_critical,
            "valid": False,
            "status": "",
            "recommendation": ""
        }
        
        if current_value is None:
            result["status"] = "MISSING"
            result["recommendation"] = f"Set {var_name}={expected_value}"
            if is_critical:
                logger.error(f"❌ {var_name}: MISSING (Critical)")
            else:
                logger.warning(f"⚠️ {var_name}: MISSING (Optional)")
        
        elif current_value == expected_value:
            result["valid"] = True
            result["status"] = "CORRECT"
            result["recommendation"] = "No action needed"
            logger.info(f"✅ {var_name}: CORRECT")
        
        else:
            result["status"] = "INCORRECT"
            result["recommendation"] = f"Change from '{current_value}' to '{expected_value}'"
            if is_critical:
                logger.error(f"❌ {var_name}: INCORRECT (Critical)")
                logger.error(f"   Current: {current_value}")
                logger.error(f"   Expected: {expected_value}")
            else:
                logger.warning(f"⚠️ {var_name}: INCORRECT (Optional)")
                logger.warning(f"   Current: {current_value}")
                logger.warning(f"   Expected: {expected_value}")
        
        return result
    
    def generate_validation_report(self):
        """Generate comprehensive validation report"""
        logger.info(f"\n{'='*60}")
        logger.info("📋 ENVIRONMENT VARIABLE VALIDATION REPORT")
        logger.info(f"{'='*60}")
        
        # Summary statistics
        total_vars = len(self.validation_results)
        valid_vars = sum(1 for r in self.validation_results.values() if r["valid"])
        critical_vars = sum(1 for r in self.validation_results.values() if r["critical"])
        critical_valid = sum(1 for r in self.validation_results.values() if r["critical"] and r["valid"])
        
        logger.info(f"Total Variables: {total_vars}")
        logger.info(f"Valid Variables: {valid_vars}/{total_vars}")
        logger.info(f"Critical Variables: {critical_valid}/{critical_vars}")
        
        # Detailed results
        logger.info(f"\n📊 DETAILED RESULTS:")
        for var_name, result in self.validation_results.items():
            status_icon = "✅" if result["valid"] else ("❌" if result["critical"] else "⚠️")
            critical_text = " (Critical)" if result["critical"] else ""
            
            logger.info(f"\n{status_icon} {var_name}{critical_text}")
            logger.info(f"   Description: {result['description']}")
            logger.info(f"   Status: {result['status']}")
            logger.info(f"   Current: {result['current_value'] or 'Not Set'}")
            logger.info(f"   Expected: {result['expected_value']}")
            if not result["valid"]:
                logger.info(f"   Action: {result['recommendation']}")
        
        # Configuration commands
        logger.info(f"\n🔧 CONFIGURATION COMMANDS:")
        logger.info("Windows (PowerShell):")
        for var_name, result in self.validation_results.items():
            if not result["valid"]:
                logger.info(f'$env:{var_name}="{result["expected_value"]}"')
        
        logger.info("\nLinux/Mac (Bash):")
        for var_name, result in self.validation_results.items():
            if not result["valid"]:
                logger.info(f'export {var_name}="{result["expected_value"]}"')
        
        # Quality check for COVE_EMAIL specifically
        logger.info(f"\n🎯 COVE_EMAIL QUALITY CHECK:")
        cove_email_result = self.validation_results.get("COVE_EMAIL")
        if cove_email_result:
            if cove_email_result["valid"]:
                logger.info("✅ COVE_EMAIL is correctly <NAME_EMAIL>")
                logger.info("✅ Email format is valid")
                logger.info("✅ Domain (kanousei.com) is properly configured")
                logger.info("✅ Cloudflare email forwarding address configured")
            else:
                logger.error("❌ COVE_EMAIL validation failed!")
                logger.error("   This is a critical configuration error")
                logger.error("   COVE authentication will fail without correct email")
        
        logger.info(f"\n{'='*60}")
    
    def fix_environment_variables(self, auto_fix: bool = False) -> bool:
        """Optionally fix environment variables automatically"""
        if not auto_fix:
            logger.info("💡 To auto-fix environment variables, run with --fix flag")
            return False
        
        logger.info("🔧 Auto-fixing environment variables...")
        
        # Create environment file
        env_content = "# Auto-generated environment variables\n"
        env_content += "# Generated by COVE Environment Validator\n\n"
        
        for var_name, result in self.validation_results.items():
            if not result["valid"]:
                env_content += f'{var_name}="{result["expected_value"]}"\n'
        
        # Write to .env file
        with open(".env", "w") as f:
            f.write(env_content)
        
        logger.info("✅ Environment variables written to .env file")
        logger.info("💡 Source the .env file or restart your shell to apply changes")
        
        return True

def main():
    """Main validation runner"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Validate COVE environment variables")
    parser.add_argument("--fix", action="store_true", help="Auto-fix environment variables")
    parser.add_argument("--email", default="<EMAIL>", help="Expected COVE email")
    
    args = parser.parse_args()
    
    # Create validator
    validator = EnvironmentValidator()
    
    # Update expected email if provided
    if args.email:
        validator.required_vars["COVE_EMAIL"]["expected"] = args.email
    
    # Run validation
    is_valid = validator.validate_all_variables()
    
    # Auto-fix if requested
    if args.fix:
        validator.fix_environment_variables(auto_fix=True)
    
    # Exit with appropriate code
    sys.exit(0 if is_valid else 1)

if __name__ == "__main__":
    main()
