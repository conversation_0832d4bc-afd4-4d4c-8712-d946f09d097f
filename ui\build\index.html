<!doctype html><html lang="en"><head><meta charset="utf-8"/><link rel="icon" href="/favicon.ico"/><meta name="viewport" content="width=device-width,initial-scale=1"/><meta name="theme-color" content="#000000"/><meta name="description" content="Cove SMART Canvas - AI-integrated strategic planning interface"/><link rel="apple-touch-icon" href="/logo192.png"/><link rel="manifest" href="/manifest.json"/><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet"><link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet"><title>Cove SMART Canvas</title><style>.loading-screen{position:fixed;top:0;left:0;width:100%;height:100%;background:linear-gradient(135deg,#667eea 0,#764ba2 100%);display:flex;flex-direction:column;justify-content:center;align-items:center;z-index:9999;color:#fff;font-family:Inter,sans-serif}.loading-logo{font-size:2.5rem;font-weight:700;margin-bottom:1rem;animation:pulse 2s infinite}.loading-text{font-size:1.1rem;opacity:.9;margin-bottom:2rem}.loading-spinner{width:40px;height:40px;border:3px solid rgba(255,255,255,.3);border-top:3px solid #fff;border-radius:50%;animation:spin 1s linear infinite}@keyframes pulse{0%,100%{opacity:1}50%{opacity:.7}}@keyframes spin{0%{transform:rotate(0)}100%{transform:rotate(360deg)}}.app-loaded .loading-screen{display:none}</style><script defer="defer" src="/static/js/main.474a3b17.js"></script><link href="/static/css/main.b7968898.css" rel="stylesheet"></head><body><noscript>You need to enable JavaScript to run this app.</noscript><div class="loading-screen" id="loading-screen"><div class="loading-logo">🎯 Cove</div><div class="loading-text">Loading SMART Canvas...</div><div class="loading-spinner"></div></div><div id="root"></div><script>window.addEventListener("load",(function(){setTimeout((function(){document.body.classList.add("app-loaded")}),1e3)}))</script></body></html>