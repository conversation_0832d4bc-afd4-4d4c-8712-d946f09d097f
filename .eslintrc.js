module.exports = {
  root: true,
  env: {
    browser: true,
    es6: true,
    node: true,
    jest: true,
  },
  extends: [
    'react-app',
    'react-app/jest'
  ],
  ignorePatterns: ['.eslintrc.js', 'build/', 'node_modules/', 'coverage/'],
  rules: {
    // Disable some strict rules for development
    '@typescript-eslint/no-unused-vars': 'warn',
    'no-console': 'warn',
    'prefer-const': 'error',
    'no-var': 'error',
  },
};
