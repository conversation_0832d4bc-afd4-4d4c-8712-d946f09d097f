import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { usePAIM } from '../contexts/PAIMContext';
import { useLoading } from '../contexts/LoadingContext';

export const PAIMAssistant = ({ selectedElements, canvasState }) => {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('suggestions');
  const [analysisResult, setAnalysisResult] = useState(null);
  
  const {
    isConnected,
    suggestions,
    generateSuggestions,
    analyzeGoal,
    optimizeCanvas,
    checkConnection,
    clearSuggestions,
    error
  } = usePAIM();
  
  const { setLoading, isLoading } = useLoading();

  useEffect(() => {
    // Check PAIM connection on mount
    checkConnection();
  }, [checkConnection]);

  const handleGenerateSuggestions = async () => {
    if (!isConnected) {
      await checkConnection();
      return;
    }

    setLoading('suggestions', true, 'Generating suggestions...');
    
    const context = {
      selectedElements: selectedElements.map(id => 
        canvasState.elements.find(el => el.id === id)
      ).filter(Boolean),
      canvasElements: canvasState.elements,
      connections: canvasState.connections
    };

    await generateSuggestions(context);
    setLoading('suggestions', false);
  };

  const handleAnalyzeGoal = async () => {
    if (selectedElements.length !== 1) return;
    
    const selectedElement = canvasState.elements.find(el => el.id === selectedElements[0]);
    if (!selectedElement || selectedElement.type !== 'smart-goal') return;

    setLoading('analysis', true, 'Analyzing SMART goal...');
    
    const analysis = await analyzeGoal(selectedElement.data);
    setAnalysisResult(analysis);
    setActiveTab('analysis');
    
    setLoading('analysis', false);
  };

  const handleOptimizeCanvas = async () => {
    if (!isConnected) {
      await checkConnection();
      return;
    }

    setLoading('optimization', true, 'Optimizing canvas layout...');
    
    const optimizations = await optimizeCanvas(canvasState);
    console.log('Canvas optimizations:', optimizations);
    
    setLoading('optimization', false);
  };

  const selectedGoals = selectedElements
    .map(id => canvasState.elements.find(el => el.id === id))
    .filter(el => el && el.type === 'smart-goal');

  return (
    <>
      {/* Floating Action Button */}
      <button 
        className={`paim-fab ${isOpen ? 'active' : ''}`}
        onClick={() => setIsOpen(!isOpen)}
        title={t('paimAssistant')}
      >
        <div className="fab-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12,2A2,2 0 0,1 14,4C14,4.74 13.6,5.39 13,5.73V7A1,1 0 0,0 14,8H18A1,1 0 0,0 19,7V5.73C18.4,5.39 18,4.74 18,4A2,2 0 0,1 20,2A2,2 0 0,1 22,4C22,5.11 21.1,6 20,6V8A3,3 0 0,1 17,11H15V12H17A3,3 0 0,1 20,15V17C21.1,17 22,17.89 22,19A2,2 0 0,1 20,21A2,2 0 0,1 18,19C18,18.26 18.4,17.61 19,17.27V15A1,1 0 0,0 18,14H14A1,1 0 0,0 13,15V17.27C13.6,17.61 14,18.26 14,19A2,2 0 0,1 12,21A2,2 0 0,1 10,19C10,18.26 10.4,17.61 11,17.27V15A1,1 0 0,0 10,14H6A1,1 0 0,0 5,15V17.27C5.6,17.61 6,18.26 6,19A2,2 0 0,1 4,21A2,2 0 0,1 2,19C2,17.89 2.9,17 4,17V15A3,3 0 0,1 7,12H9V11H7A3,3 0 0,1 4,8V6C2.9,6 2,5.11 2,4A2,2 0 0,1 4,2A2,2 0 0,1 6,4C6,4.74 5.6,5.39 5,5.73V7A1,1 0 0,0 6,8H10A1,1 0 0,0 11,7V5.73C10.4,5.39 10,4.74 10,4A2,2 0 0,1 12,2Z"/>
          </svg>
        </div>
        
        {!isConnected && (
          <div className="connection-indicator offline" />
        )}
      </button>

      {/* Assistant Panel */}
      {isOpen && (
        <div className="paim-panel">
          <div className="panel-header">
            <h3>{t('paimAssistant')}</h3>
            <button 
              className="close-button"
              onClick={() => setIsOpen(false)}
            >
              ×
            </button>
          </div>

          <div className="panel-tabs">
            <button 
              className={`tab ${activeTab === 'suggestions' ? 'active' : ''}`}
              onClick={() => setActiveTab('suggestions')}
            >
              {t('suggestions')}
            </button>
            <button 
              className={`tab ${activeTab === 'analysis' ? 'active' : ''}`}
              onClick={() => setActiveTab('analysis')}
              disabled={selectedGoals.length === 0}
            >
              {t('analysis')}
            </button>
          </div>

          <div className="panel-content">
            {!isConnected && (
              <div className="connection-warning">
                <p>{t('paimNotConnected')}</p>
                <button onClick={checkConnection}>
                  {t('reconnect')}
                </button>
              </div>
            )}

            {error && (
              <div className="error-message">
                <p>{error}</p>
              </div>
            )}

            {activeTab === 'suggestions' && (
              <div className="suggestions-tab">
                <div className="tab-actions">
                  <button 
                    className="action-button primary"
                    onClick={handleGenerateSuggestions}
                    disabled={!isConnected || isLoading('suggestions')}
                  >
                    {isLoading('suggestions') ? t('loading') : t('generateSuggestions')}
                  </button>
                  
                  <button 
                    className="action-button"
                    onClick={handleOptimizeCanvas}
                    disabled={!isConnected || isLoading('optimization') || canvasState.elements.length === 0}
                  >
                    {isLoading('optimization') ? t('loading') : t('optimizeCanvas')}
                  </button>
                  
                  {suggestions.length > 0 && (
                    <button 
                      className="action-button secondary"
                      onClick={clearSuggestions}
                    >
                      {t('clear')}
                    </button>
                  )}
                </div>

                <div className="suggestions-list">
                  {suggestions.length === 0 ? (
                    <p className="empty-state">{t('noSuggestions')}</p>
                  ) : (
                    suggestions.map((suggestion, index) => (
                      <div key={index} className="suggestion-item">
                        <div className="suggestion-type">{suggestion.type}</div>
                        <div className="suggestion-text">{suggestion.text}</div>
                        {suggestion.action && (
                          <button 
                            className="suggestion-action"
                            onClick={() => console.log('Apply suggestion:', suggestion)}
                          >
                            {t('apply')}
                          </button>
                        )}
                      </div>
                    ))
                  )}
                </div>
              </div>
            )}

            {activeTab === 'analysis' && (
              <div className="analysis-tab">
                <div className="tab-actions">
                  <button 
                    className="action-button primary"
                    onClick={handleAnalyzeGoal}
                    disabled={!isConnected || selectedGoals.length !== 1 || isLoading('analysis')}
                  >
                    {isLoading('analysis') ? t('loading') : t('analyzeGoal')}
                  </button>
                </div>

                {selectedGoals.length === 0 && (
                  <p className="empty-state">{t('selectSmartGoal')}</p>
                )}

                {selectedGoals.length > 1 && (
                  <p className="empty-state">{t('selectSingleGoal')}</p>
                )}

                {analysisResult && (
                  <div className="analysis-result">
                    <h4>{t('analysisResult')}</h4>
                    <div className="analysis-content">
                      {analysisResult.score && (
                        <div className="score-section">
                          <span className="score-label">{t('smartScore')}</span>
                          <span className="score-value">{analysisResult.score}/100</span>
                        </div>
                      )}
                      
                      {analysisResult.recommendations && (
                        <div className="recommendations">
                          <h5>{t('recommendations')}</h5>
                          <ul>
                            {analysisResult.recommendations.map((rec, index) => (
                              <li key={index}>{rec}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      <style jsx>{`
        .paim-fab {
          position: fixed;
          bottom: 24px;
          right: 24px;
          width: 56px;
          height: 56px;
          border-radius: 50%;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border: none;
          color: white;
          cursor: pointer;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
          z-index: 1000;
          position: relative;
        }
        
        .paim-fab:hover {
          transform: scale(1.1);
          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }
        
        .paim-fab.active {
          background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        }
        
        .fab-icon {
          transition: transform 0.3s ease;
        }
        
        .paim-fab.active .fab-icon {
          transform: rotate(180deg);
        }
        
        .connection-indicator {
          position: absolute;
          top: 4px;
          right: 4px;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #28a745;
        }
        
        .connection-indicator.offline {
          background: #dc3545;
        }
        
        .paim-panel {
          position: fixed;
          bottom: 100px;
          right: 24px;
          width: 400px;
          max-height: 600px;
          background: white;
          border-radius: 12px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
          z-index: 999;
          display: flex;
          flex-direction: column;
          overflow: hidden;
        }
        
        .panel-header {
          padding: 16px 20px;
          border-bottom: 1px solid #e1e5e9;
          display: flex;
          justify-content: space-between;
          align-items: center;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
        }
        
        .panel-header h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
        }
        
        .close-button {
          background: none;
          border: none;
          color: white;
          font-size: 24px;
          cursor: pointer;
          padding: 0;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .panel-tabs {
          display: flex;
          background: #f8f9fa;
          border-bottom: 1px solid #e1e5e9;
        }
        
        .tab {
          flex: 1;
          padding: 12px 16px;
          background: none;
          border: none;
          cursor: pointer;
          font-size: 14px;
          color: #6c757d;
          transition: all 0.2s ease;
        }
        
        .tab:hover {
          background: #e9ecef;
        }
        
        .tab.active {
          background: white;
          color: #495057;
          font-weight: 500;
        }
        
        .tab:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
        
        .panel-content {
          flex: 1;
          overflow-y: auto;
          padding: 20px;
        }
        
        .connection-warning,
        .error-message {
          padding: 12px;
          border-radius: 6px;
          margin-bottom: 16px;
        }
        
        .connection-warning {
          background: #fff3cd;
          border: 1px solid #ffeaa7;
          color: #856404;
        }
        
        .error-message {
          background: #f8d7da;
          border: 1px solid #f5c6cb;
          color: #721c24;
        }
        
        .tab-actions {
          display: flex;
          flex-direction: column;
          gap: 8px;
          margin-bottom: 20px;
        }
        
        .action-button {
          padding: 10px 16px;
          border: 1px solid #dee2e6;
          border-radius: 6px;
          background: white;
          cursor: pointer;
          font-size: 14px;
          transition: all 0.2s ease;
        }
        
        .action-button:hover {
          background: #f8f9fa;
        }
        
        .action-button.primary {
          background: #007bff;
          color: white;
          border-color: #007bff;
        }
        
        .action-button.primary:hover {
          background: #0056b3;
        }
        
        .action-button.secondary {
          background: #6c757d;
          color: white;
          border-color: #6c757d;
        }
        
        .action-button:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
        
        .empty-state {
          text-align: center;
          color: #6c757d;
          font-style: italic;
          margin: 40px 0;
        }
        
        .suggestions-list {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }
        
        .suggestion-item {
          padding: 12px;
          border: 1px solid #e1e5e9;
          border-radius: 6px;
          background: #f8f9fa;
        }
        
        .suggestion-type {
          font-size: 12px;
          color: #007bff;
          font-weight: 500;
          text-transform: uppercase;
          margin-bottom: 4px;
        }
        
        .suggestion-text {
          font-size: 14px;
          color: #495057;
          margin-bottom: 8px;
        }
        
        .suggestion-action {
          background: #28a745;
          color: white;
          border: none;
          padding: 4px 12px;
          border-radius: 4px;
          font-size: 12px;
          cursor: pointer;
        }
        
        .analysis-result {
          border: 1px solid #e1e5e9;
          border-radius: 6px;
          padding: 16px;
          background: #f8f9fa;
        }
        
        .analysis-result h4 {
          margin: 0 0 12px 0;
          font-size: 14px;
          color: #495057;
        }
        
        .score-section {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
          padding: 8px 12px;
          background: white;
          border-radius: 4px;
        }
        
        .score-label {
          font-size: 14px;
          color: #6c757d;
        }
        
        .score-value {
          font-size: 18px;
          font-weight: 600;
          color: #28a745;
        }
        
        .recommendations h5 {
          margin: 0 0 8px 0;
          font-size: 13px;
          color: #495057;
        }
        
        .recommendations ul {
          margin: 0;
          padding-left: 16px;
        }
        
        .recommendations li {
          font-size: 13px;
          color: #6c757d;
          margin-bottom: 4px;
        }
      `}</style>
    </>
  );
};

// Add default export
export default PAIMAssistant;