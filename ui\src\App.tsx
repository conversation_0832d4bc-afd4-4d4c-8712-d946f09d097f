/**
 * Cove Application with Design System Integration
 * Phase 1.1 - Design System & Accessibility Foundation Implementation
 */

import React, { useState } from 'react';
import './App.css';

// Import Design System
import {
  ThemeProvider,
  useTheme,
  Button,
  Text,
  Container,
  Input
} from './design-system';

// Import Voice System - Temporarily commented out
// import { VoiceProvider } from './design-system/voice/VoiceProvider';
// import VoiceInput from './design-system/components/voice/VoiceInput';
// import VoiceOutput from './design-system/components/voice/VoiceOutput';
// import VoiceNavigation from './design-system/components/voice/VoiceNavigation';
// import VoiceSettings from './design-system/components/voice/VoiceSettings';

// Import Design System CSS Variables
import './design-system/styles/variables.css';

// Import Design System Demo
import DesignSystemDemo from './design-system/demo/DesignSystemDemo';

// Import existing components
import SmartCanvas from './components/SmartCanvas';
// import PAIMAssistant from './components/PAIMAssistant';
// import AIgencyIntegrationDemo from './components/AIgencyIntegrationDemo';

// Import Enhanced PAIM Provider - Temporarily commented out
// import { EnhancedPAIMProvider } from './paim/providers/EnhancedPAIMProvider';

// Navigation Component
const Navigation = () => {
  const { language, switchLanguage } = useTheme();
  
  return (
    <nav style={{ 
      padding: '1rem 0', 
      borderBottom: '1px solid var(--cove-color-border-light)',
      marginBottom: '2rem'
    }}>
      <Container size="xl">
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: '1rem'
        }}>
          <Text variant="h3" weight="bold">
            {language === 'ar' ? 'كوف - منصة الذكاء الاصطناعي' : 'Cove - AI Platform'}
          </Text>
          
          <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
            <Button
              variant={language === 'en' ? 'primary' : 'secondary'}
              size="sm"
              onClick={() => switchLanguage('en')}
            >
              English
            </Button>
            <Button
              variant={language === 'ar' ? 'primary' : 'secondary'}
              size="sm"
              onClick={() => switchLanguage('ar')}
            >
              العربية
            </Button>
          </div>
        </div>
      </Container>
    </nav>
  );
};

// Main App Content
const AppContent = () => {
  const [currentView, setCurrentView] = useState('design-system');
  const { language } = useTheme();

  const views = {
    'design-system': {
      title: language === 'ar' ? 'نظام التصميم' : 'Design System',
      component: <DesignSystemDemo />
    },
    'smart-canvas': {
      title: language === 'ar' ? 'اللوحة الذكية' : 'Smart Canvas',
      component: <SmartCanvas />
    },
    // 'paim-assistant': {
    //   title: language === 'ar' ? 'مساعد PAIM' : 'PAIM Assistant',
    //   component: <PAIMAssistant />
    // },
    // 'voice-demo': {
    //   title: language === 'ar' ? 'تجربة الصوت' : 'Voice Demo',
    //   component: (
    //     <div>
    //       <VoiceInput />
    //       <VoiceOutput />
    //       <VoiceNavigation />
    //       <VoiceSettings />
    //     </div>
    //   )
    // },
    // 'aigency-integration': {
    //   title: language === 'ar' ? 'تكامل AIgency' : 'AIgency Integration',
    //   component: <AIgencyIntegrationDemo />
    // }
  };

  return (
    <div style={{ minHeight: '100vh', backgroundColor: 'var(--cove-color-background-light)' }}>
      <Navigation />
      
      <Container size="xl" padding="lg">
        {/* View Selector */}
        <div style={{ marginBottom: '2rem' }}>
          <Text variant="h4" style={{ marginBottom: '1rem' }}>
            {language === 'ar' ? 'اختر العرض:' : 'Select View:'}
          </Text>
          <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
            {Object.entries(views).map(([key, view]) => (
              <Button
                key={key}
                variant={currentView === key ? 'primary' : 'secondary'}
                onClick={() => setCurrentView(key)}
              >
                {view.title}
              </Button>
            ))}
          </div>
        </div>

        {/* Current View */}
        <div>
          {views[currentView].component}
        </div>

        {/* Implementation Status */}
        {currentView === 'design-system' && (
          <Container 
            padding="lg" 
            style={{ 
              marginTop: '3rem',
              backgroundColor: 'var(--cove-color-background-elevated)', 
              borderRadius: '8px',
              border: '1px solid var(--cove-color-border-light)'
            }}
          >
            <Text variant="h3" style={{ marginBottom: '1.5rem' }}>
              {language === 'ar' ? 'حالة التنفيذ - المرحلة 1.1' : 'Implementation Status - Phase 1.1'}
            </Text>
            
            <div style={{ display: 'grid', gap: '1rem' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <span style={{ color: 'var(--cove-color-success-default)', fontSize: '1.2em' }}>✅</span>
                <Text variant="body">
                  {language === 'ar' 
                    ? 'نظام الرموز المميزة للتصميم مع التكيف الثقافي'
                    : 'Design Token System with Cultural Adaptation'
                  }
                </Text>
              </div>
              
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <span style={{ color: 'var(--cove-color-success-default)', fontSize: '1.2em' }}>✅</span>
                <Text variant="body">
                  {language === 'ar' 
                    ? 'مكتبة المكونات مع إمكانية الوصول الكاملة (WCAG 2.1 AA)'
                    : 'Component Library with Full Accessibility (WCAG 2.1 AA)'
                  }
                </Text>
              </div>
              
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <span style={{ color: 'var(--cove-color-success-default)', fontSize: '1.2em' }}>✅</span>
                <Text variant="body">
                  {language === 'ar' 
                    ? 'نظام التخطيط من اليمين لليسار مع الطباعة العربية'
                    : 'RTL Layout System with Arabic Typography'
                  }
                </Text>
              </div>
              
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <span style={{ color: 'var(--cove-color-success-default)', fontSize: '1.2em' }}>✅</span>
                <Text variant="body">
                  {language === 'ar' 
                    ? 'نظام الألوان والسمات مع نسب التباين AAA'
                    : 'Color & Theme System with AAA Contrast Ratios'
                  }
                </Text>
              </div>
              
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <span style={{ color: 'var(--cove-color-success-default)', fontSize: '1.2em' }}>✅</span>
                <Text variant="body">
                  {language === 'ar' 
                    ? 'تحسين الطباعة والخطوط (<200ms تحميل)'
                    : 'Typography & Font Optimization (<200ms loading)'
                  }
                </Text>
              </div>
              
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <span style={{ color: 'var(--cove-color-success-default)', fontSize: '1.2em' }}>✅</span>
                <Text variant="body">
                  {language === 'ar' 
                    ? 'اختبار إمكانية الوصول الآلي مع axe-core'
                    : 'Automated Accessibility Testing with axe-core'
                  }
                </Text>
              </div>
            </div>
            
            <div style={{ marginTop: '1.5rem', padding: '1rem', backgroundColor: 'var(--cove-color-background-paper)', borderRadius: '4px' }}>
              <Text variant="bodySmall" weight="medium" style={{ marginBottom: '0.5rem' }}>
                {language === 'ar' ? 'الميزات الرئيسية:' : 'Key Features:'}
              </Text>
              <Text variant="bodySmall">
                {language === 'ar' 
                  ? '• دعم كامل للغة العربية والتخطيط من اليمين لليسار • متوافق مع WCAG 2.1 AA/AAA • تكيف ثقافي لمنطقة الخليج • أداء محسن (<5% إضافة) • دعم الحركة المحترمة • خطوط مناسبة لعسر القراءة'
                  : '• Full Arabic & RTL Support • WCAG 2.1 AA/AAA Compliant • Gulf Region Cultural Adaptation • Performance Optimized (<5% overhead) • Respectful Motion Support • Dyslexia-Friendly Fonts'
                }
              </Text>
            </div>
          </Container>
        )}
      </Container>
    </div>
  );
};

// Main App Component
function App() {
  return (
    <ThemeProvider defaultTheme="auto" defaultLanguage="en">
      {/* <EnhancedPAIMProvider> */}
        {/* <VoiceProvider config={{ localProcessingPreferred: true, defaultDialect: 'StandardArabic', defaultCulturalCharacteristics: { tone: 'neutral', pace: 'medium', formality: 'medium', gender: 'neutral' } }}> */}
          <AppContent />
        {/* </VoiceProvider> */}
      {/* </EnhancedPAIMProvider> */}
    </ThemeProvider>
  );
}

export default App;