# COVE-AIgency Integration Setup Guide

This guide will help you set up and test the COVE-AIgency integration step by step.

## 🚀 Quick Start (Simplified Version)

### Step 1: Start the Simple COVE Service

First, let's start with a simplified version that doesn't have complex dependencies:

```bash
cd backend/services
python simple_start.py
```

You should see output like:
```
INFO:__main__:Starting Simple COVE Integration Service...
INFO:__main__:Simple COVE Integration Service starting on 0.0.0.0:8000
INFO:__main__:Available endpoints:
INFO:__main__:  - Main API: http://localhost:8000
INFO:__main__:  - Health Check: http://localhost:8000/health
INFO:__main__:  - Test Endpoint: http://localhost:8000/test
INFO:__main__:  - Cultural Test: http://localhost:8000/api/v1/test/cultural
INFO:__main__:  - AIgency Test: http://localhost:8000/api/v1/test/aigency
INFO:__main__:  - API Documentation: http://localhost:8000/docs
```

### Step 2: Test the Simple Service

In a new terminal, run the simple test:

```bash
cd backend/services
python simple_test.py
```

This will test basic functionality and connectivity.

### Step 3: Check Service in Browser

Open your browser and visit:
- **Main API**: http://localhost:8000
- **Health Check**: http://localhost:8000/health
- **API Documentation**: http://localhost:8000/docs

## 🔧 Full Integration Setup

Once the simple version is working, you can set up the full integration:

### Prerequisites

1. **Python 3.11+** with virtual environment
2. **The AIgency Backend** (should be running on localhost:3000)
3. **Redis Server** (for caching and sessions)

### Step 1: Install Dependencies

```bash
cd backend/services
pip install -r requirements.txt
```

### Step 2: Set Environment Variables

```bash
# Windows (PowerShell)
$env:AIGENCY_URL="http://localhost:3000"
$env:REDIS_URL="redis://localhost:6379"
$env:COVE_EMAIL="<EMAIL>"
$env:COVE_PASSWORD="CoveSecure123!"

# Linux/Mac (Bash)
export AIGENCY_URL="http://localhost:3000"
export REDIS_URL="redis://localhost:6379"
export COVE_EMAIL="<EMAIL>"
export COVE_PASSWORD="CoveSecure123!"
```

### Step 3: Start Redis (if not already running)

```bash
# Windows (if Redis is installed)
redis-server

# Linux/Mac
redis-server

# Or using Docker
docker run -d -p 6379:6379 redis:alpine
```

### Step 4: Start The AIgency Backend

```bash
# Navigate to The AIgency project directory
cd /path/to/theaigency
npm install
npm start
```

The AIgency should be running on http://localhost:3000

### Step 5: Start Full COVE Integration

```bash
cd backend/services
python start_cove_integration.py
```

### Step 6: Run Full Integration Tests

```bash
cd backend/services
python test_integration.py
```

## 🐛 Troubleshooting

### Common Issues and Solutions

#### 1. Import Errors

**Error**: `ImportError: attempted relative import beyond top-level package`

**Solution**: Use the simple version first:
```bash
python simple_start.py
```

#### 2. The AIgency Connection Failed

**Error**: `Server disconnected without sending a response`

**Solutions**:
- Ensure The AIgency is running: `curl http://localhost:3000/health`
- Check if port 3000 is available
- Verify The AIgency startup logs for errors

#### 3. Redis Connection Failed

**Error**: `redis.exceptions.ConnectionError`

**Solutions**:
- Start Redis server: `redis-server`
- Check if Redis is running: `redis-cli ping`
- Use Docker if Redis installation is problematic: `docker run -d -p 6379:6379 redis:alpine`

#### 4. Port Already in Use

**Error**: `OSError: [Errno 48] Address already in use`

**Solutions**:
- Change the port: `export COVE_PORT=8001`
- Kill existing process: `lsof -ti:8000 | xargs kill -9` (Mac/Linux)
- Use different port in startup command

### Testing Individual Components

#### Test COVE Service Only
```bash
python simple_start.py
# Then visit http://localhost:8000/health
```

#### Test AIgency Connection
```bash
curl http://localhost:3000/health
# Should return JSON with status information
```

#### Test Redis Connection
```bash
redis-cli ping
# Should return "PONG"
```

## 📋 Verification Checklist

### ✅ Basic Setup Working
- [ ] Simple COVE service starts without errors
- [ ] Health check endpoint returns "healthy"
- [ ] API documentation is accessible at /docs
- [ ] Simple test script passes all tests

### ✅ Full Integration Working
- [ ] The AIgency backend is running on localhost:3000
- [ ] Redis server is running and accessible
- [ ] Full COVE integration service starts without errors
- [ ] Integration test script passes most tests
- [ ] Can create PAIMs through the API
- [ ] Cultural adaptation features work

### ✅ Production Ready
- [ ] All environment variables are set
- [ ] Security credentials are changed from defaults
- [ ] Monitoring and logging are configured
- [ ] Error handling is working properly

## 🎯 Next Steps

Once you have the basic integration working:

1. **Frontend Integration**: Update the COVE UI to use the new API endpoints
2. **Cultural Enhancement**: Add more Arabic dialects and cultural rules
3. **PowerOps Integration**: Implement gamification features
4. **Production Deployment**: Set up proper hosting and monitoring

## 📞 Getting Help

If you encounter issues:

1. **Check the logs** - Both COVE service and The AIgency logs
2. **Verify prerequisites** - Ensure all required services are running
3. **Test components individually** - Use the simple test scripts
4. **Check network connectivity** - Ensure services can communicate

## 🔗 Useful Commands

```bash
# Check if services are running
curl http://localhost:8000/health  # COVE service
curl http://localhost:3000/health  # The AIgency
redis-cli ping                     # Redis

# View logs
tail -f logs/cove_integration.log  # COVE logs (if configured)

# Kill processes if needed
lsof -ti:8000 | xargs kill -9      # Kill COVE service
lsof -ti:3000 | xargs kill -9      # Kill AIgency service
```

## 📚 Additional Resources

- **API Documentation**: http://localhost:8000/docs (when service is running)
- **The AIgency Documentation**: Check The AIgency project README
- **Redis Documentation**: https://redis.io/documentation
- **FastAPI Documentation**: https://fastapi.tiangolo.com/
