"""
Simple COVE Integration Startup Script
A simplified version that starts just the cultural adaptation service
"""

import asyncio
import logging
import os
import sys
from typing import Dict, Any

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_config() -> Dict[str, Any]:
    """Load configuration from environment variables"""
    return {
        # Server configuration
        "host": os.getenv("COVE_HOST", "0.0.0.0"),
        "port": int(os.getenv("COVE_PORT", "8000")),

        # Service URLs
        "aigency_url": os.getenv("AIGENCY_URL", "http://localhost:3000"),
        "base_url": os.getenv("COVE_BASE_URL", "http://localhost:8000"),
        
        # COVE credentials
        "cove_email": os.getenv("COVE_EMAIL", "<EMAIL>"),
        "cove_password": os.getenv("COVE_PASSWORD", "CoveSecure123!"),
        
        # Cultural service configuration
        "cultural": {
            "redis_url": os.getenv("REDIS_URL", "redis://localhost:6379")
        },
        
        # CORS
        "allowed_origins": os.getenv("ALLOWED_ORIGINS", "*").split(","),
        
        # Logging
        "log_level": os.getenv("LOG_LEVEL", "INFO")
    }

def create_simple_app() -> FastAPI:
    """Create a simple FastAPI application for testing"""
    config = load_config()
    
    # Create main app
    app = FastAPI(
        title="COVE Integration Service",
        description="Simple COVE integration service for testing",
        version="1.0.0"
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=config["allowed_origins"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    @app.get("/")
    async def root():
        """Root endpoint"""
        return {
            "service": "COVE Integration Service",
            "version": "1.0.0",
            "status": "running",
            "endpoints": {
                "health": "/health",
                "test": "/test",
                "docs": "/docs"
            }
        }
    
    @app.get("/health")
    async def health_check():
        """Health check endpoint"""
        return {
            "status": "healthy",
            "service": "cove_integration",
            "timestamp": "2024-12-01T00:00:00Z",
            "config": {
                "aigency_url": config["aigency_url"],
                "redis_url": config["cultural"]["redis_url"]
            }
        }
    
    @app.get("/test")
    async def test_endpoint():
        """Test endpoint to verify service is working"""
        return {
            "message": "COVE Integration Service is working!",
            "test_passed": True,
            "timestamp": "2024-12-01T00:00:00Z"
        }
    
    @app.post("/api/v1/test/cultural")
    async def test_cultural_adaptation():
        """Test cultural adaptation functionality"""
        return {
            "message": "Cultural adaptation test endpoint",
            "supported_dialects": ["gulf_uae", "gulf_saudi", "levantine"],
            "features": [
                "dialect_detection",
                "formality_adaptation", 
                "religious_sensitivity",
                "cultural_context"
            ]
        }
    
    @app.post("/api/v1/test/aigency")
    async def test_aigency_connection():
        """Test AIgency connection"""
        import httpx
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{config['aigency_url']}/health", timeout=5.0)
                
                if response.status_code == 200:
                    return {
                        "aigency_status": "connected",
                        "aigency_url": config["aigency_url"],
                        "response_code": response.status_code,
                        "message": "Successfully connected to The AIgency backend"
                    }
                else:
                    return {
                        "aigency_status": "error",
                        "aigency_url": config["aigency_url"],
                        "response_code": response.status_code,
                        "message": f"The AIgency returned status {response.status_code}"
                    }
        except Exception as e:
            return {
                "aigency_status": "unreachable",
                "aigency_url": config["aigency_url"],
                "error": str(e),
                "message": "Could not connect to The AIgency backend"
            }
    
    return app

async def main():
    """Main entry point"""
    try:
        logger.info("Starting Simple COVE Integration Service...")
        
        # Load configuration
        config = load_config()
        
        # Create application
        app = create_simple_app()
        
        # Start server
        uvicorn_config = uvicorn.Config(
            app,
            host=config["host"],
            port=config["port"],
            log_level=config["log_level"].lower(),
            access_log=True
        )
        
        server = uvicorn.Server(uvicorn_config)
        
        logger.info(f"Simple COVE Integration Service starting on {config['host']}:{config['port']}")
        logger.info("Available endpoints:")
        logger.info("  - Main API: http://localhost:8000")
        logger.info("  - Health Check: http://localhost:8000/health")
        logger.info("  - Test Endpoint: http://localhost:8000/test")
        logger.info("  - Cultural Test: http://localhost:8000/api/v1/test/cultural")
        logger.info("  - AIgency Test: http://localhost:8000/api/v1/test/aigency")
        logger.info("  - API Documentation: http://localhost:8000/docs")
        
        await server.serve()
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
    except Exception as e:
        logger.error(f"Server error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
