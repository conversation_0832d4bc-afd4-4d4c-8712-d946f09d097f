{"version": 3, "file": "static/css/main.b7968898.css", "mappings": "AAkBA,gBACE,WAAY,CACZ,eACF,CCpBA,EACE,qBACF,CAEA,KAOE,wBAAyB,CALzB,mIAEY,CAHZ,QAOF,CAEA,KACE,uEAEF,CAGA,KAEE,YAAa,CACb,qBAAsB,CAFtB,YAAa,CAGb,eACF,CAGA,wBAGE,wBAEF,CAEA,8BACE,YACF,CAGA,aAIE,eACF,CAEA,kBAIE,qBAAyB,CACzB,oGAEkE,CAClE,yBAEF,CAOA,mBAEE,eAAiB,CACjB,8BAA+B,CAG/B,8BAAyC,CAFzC,YAAa,CACb,qBAAsB,CAJtB,WAAY,CAMZ,UACF,CAEA,gBAGE,kDAA6D,CAD7D,+BAAgC,CAEhC,UAAY,CAHZ,YAIF,CAEA,mBAEE,cAAe,CACf,eAAgB,CAFhB,QAGF,CAEA,iBACE,QAAO,CAEP,eAAgB,CADhB,YAEF,CAEA,oBACE,kBACF,CAEA,gBAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAIhB,mBAAqB,CAFrB,kBAAmB,CACnB,wBAEF,CAEA,gBACE,YAAa,CACb,qBAAsB,CACtB,OACF,CAEA,gBAQE,kBAAmB,CAJnB,kBAAmB,CAFnB,wBAAyB,CACzB,iBAAkB,CAElB,WAAY,CAEZ,YAAa,CAEb,QAAS,CART,iBAAkB,CAKlB,uBAIF,CAEA,sBACE,kBAAmB,CACnB,oBAAqB,CAErB,8BAAwC,CADxC,0BAEF,CAEA,uBACE,eAAgB,CAChB,uBACF,CAEA,gBAGE,aAAc,CADd,WAAY,CADZ,UAGF,CAEA,gBACE,QACF,CAEA,gBAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,iBACF,CAEA,uBAEE,aAAc,CADd,cAEF,CAGA,aAGE,MAAO,CAFP,iBAAkB,CAClB,KAIF,CAGA,gBAEE,eAAiB,CACjB,wBAAyB,CACzB,iBAAkB,CAClB,8BAAwC,CACxC,WAAY,CALZ,iBAAkB,CAMlB,uBAAyB,CACzB,wBAAiB,CAAjB,gBACF,CAEA,sBACE,oBAAqB,CACrB,+BACF,CAEA,yBACE,oBAAqB,CACrB,8BACF,CAEA,yBAGE,2BAAyC,CADzC,sBAAuB,CADvB,YAGF,CAGA,oBAEE,+CAA6D,CAD7D,oBAEF,CAEA,6BACE,oBAAqB,CACrB,8BACF,CAEA,cAEE,+CAA6D,CAD7D,oBAEF,CAEA,uBACE,oBAAqB,CACrB,8BACF,CAEA,aAEE,+CAA6D,CAD7D,oBAEF,CAEA,sBACE,oBAAqB,CACrB,8BACF,CAEA,qBAEE,+CAA6D,CAD7D,oBAEF,CAEA,8BACE,oBAAqB,CACrB,8BACF,CAGA,gBAGE,oBAA+B,CAD/B,+BAAgC,CADhC,iBAMF,CAEA,eAGE,aAAc,CAFd,cAIF,CAEA,oBAIE,kBAAmB,CADnB,iBAAkB,CAElB,aAAc,CAJd,cAAe,CAMf,mBAAqB,CALrB,eAAgB,CAIhB,wBAEF,CAcA,aAGE,aAIF,CAOA,0BAHE,aAUF,CAPA,aAGE,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAHf,eAAgB,CADhB,UAMF,CAEA,mBAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAGA,cAGE,kBAGF,CAEA,eAEE,iDAEF,CAGA,kBACE,mBAKF,CAQA,gCACE,kBACF,CAEA,gCACE,kBACF,CAEA,8BACE,kBACF,CAEA,4BACE,kBACF,CAGA,oBAEE,kBAAmB,CADnB,mBAAoB,CAEpB,OAKF,CAEA,cACE,kBAAmB,CACnB,aACF,CAEA,iBACE,kBAAmB,CACnB,aACF,CAEA,eACE,kBAAmB,CACnB,aACF,CAEA,iBAGE,2BAA4B,CAF5B,kBAAmB,CACnB,aAEF,CAEA,iBACE,GAAK,SAAY,CACjB,IAAM,UAAc,CACpB,GAAO,SAAY,CACrB,CAGA,yBACE,wBACE,qBACF,CAEA,mBAIE,+BAAgC,CADhC,iBAAkB,CADlB,YAAa,CADb,UAIF,CAEA,iBACE,YACF,CAEA,gBACE,kBAAmB,CAEnB,QAAS,CADT,eAEF,CAEA,gBAEE,aAAc,CADd,eAEF,CACF,CAGA,uCACE,EACE,kCAAqC,CACrC,qCAAuC,CACvC,mCACF,CACF,CAGA,+BACE,gBACE,gBACF,CAEA,gBACE,oBACF,CAEA,mBACE,gBACF,CACF,CAGA,aACE,yCAGE,sBACF,CAEA,aACE,oBACF,CAEA,kBACE,yBACF,CAEA,gBAEE,+BAAiC,CADjC,yBAEF,CACF,CCzcA,MAEE,4BAA6B,CAC7B,+BAAgC,CAChC,gCAAiC,CACjC,gCAAiC,CACjC,gCAAiC,CACjC,gCAAiC,CACjC,gCAAiC,CACjC,gCAAiC,CACjC,gCAAiC,CACjC,gCAAiC,CACjC,gCAAiC,CACjC,gCAAiC,CAEjC,2BAA4B,CAC5B,8BAA+B,CAC/B,+BAAgC,CAChC,+BAAgC,CAChC,+BAAgC,CAChC,+BAAgC,CAChC,+BAAgC,CAChC,+BAAgC,CAChC,+BAAgC,CAChC,+BAAgC,CAChC,+BAAgC,CAChC,+BAAgC,CAEhC,4BAA6B,CAC7B,2BAA4B,CAC5B,6BAA8B,CAC9B,4BAA6B,CAE7B,qCAAsC,CACtC,oCAAqC,CACrC,kCAAsC,CACtC,wCAAyC,CAEzC,iCAAkC,CAClC,mCAAoC,CACpC,kCAAmC,CACnC,8BAAkC,CAClC,kCAAmC,CAEnC,iCAAkC,CAClC,kCAAmC,CACnC,gCAAiC,CACjC,iCAAkC,CAGlC,8DAAkE,CAClE,0DAA6D,CAC7D,qEAAwE,CACxE,6DAAgE,CAEhE,2BAA4B,CAC5B,4BAA6B,CAC7B,0BAA2B,CAC3B,4BAA6B,CAC7B,2BAA4B,CAC5B,2BAA4B,CAC5B,6BAA8B,CAC9B,4BAA6B,CAC7B,yBAA0B,CAC1B,4BAA6B,CAC7B,2BAA4B,CAC5B,yBAA0B,CAC1B,yBAA0B,CAE1B,2BAA4B,CAC5B,iCAAkC,CAClC,4BAA6B,CAC7B,6BAA8B,CAC9B,6BAA8B,CAC9B,+BAAgC,CAChC,2BAA4B,CAC5B,gCAAiC,CACjC,4BAA6B,CAC7B,6BAA8B,CAC9B,2BAA4B,CAE5B,yBAA0B,CAC1B,6BAA8B,CAC9B,6BAA8B,CAC9B,6BAA8B,CAC9B,gCAAiC,CACjC,0BAA2B,CAE3B,qCAAsC,CACtC,oCAAqC,CACrC,gCAAiC,CACjC,kCAAmC,CACnC,kCAAmC,CACnC,kCAAmC,CAGnC,oBAAqB,CACrB,wBAAyB,CACzB,uBAAwB,CACxB,wBAAyB,CACzB,qBAAsB,CACtB,wBAAyB,CACzB,uBAAwB,CACxB,qBAAsB,CACtB,wBAAyB,CACzB,sBAAuB,CACvB,sBAAuB,CACvB,sBAAuB,CACvB,sBAAuB,CACvB,sBAAuB,CACvB,uBAAwB,CACxB,uBAAwB,CACxB,uBAAwB,CACxB,uBAAwB,CAGxB,6BAA8B,CAC9B,gCAAiC,CACjC,oCAAqC,CACrC,gCAAiC,CACjC,8BAA+B,CAC/B,+BAAgC,CAChC,6BAA8B,CAC9B,+BAAgC,CAChC,gCAAiC,CACjC,kCAAmC,CACnC,+BAAgC,CAGhC,sCAA+C,CAC/C,oEAAoF,CACpF,kEAAkF,CAClF,oEAAoF,CACpF,qEAAqF,CACrF,6CAAsD,CACtD,+CAAwD,CACxD,uBAAwB,CAGxB,wBAAyB,CACzB,kBAAmB,CACnB,oBAAqB,CACrB,oBAAqB,CACrB,oBAAqB,CACrB,oBAAqB,CACrB,oBAAqB,CACrB,4BAA6B,CAC7B,0BAA2B,CAC3B,yBAA0B,CAC1B,yBAA0B,CAC1B,2BAA4B,CAC5B,2BAA4B,CAC5B,yBAA0B,CAG1B,kCAAmC,CACnC,iCAAkC,CAClC,mCAAoC,CACpC,iCAAkC,CAClC,mCAAoC,CACpC,oCAAqC,CACrC,oCAAqC,CACrC,qCAAsC,CACtC,uCAAwC,CAExC,kCAAmC,CACnC,8BAA+B,CAC/B,oCAAqC,CACrC,sCAAuC,CACvC,4CAA6C,CAC7C,2DAA+D,CAC/D,8DAAkE,CAClE,4DAAgE,CAChE,6DAAiE,CACjE,uDAA2D,CAC3D,8DAAkE,CAGlE,gCAAiC,CACjC,kCAAmC,CACnC,oCAAqC,CACrC,iCAAkC,CAElC,qCAAsC,CACtC,yCAA0C,CAC1C,yCAA0C,CAG1C,0FAA8F,CAC9F,qFAAyF,CACzF,mEAAuE,CAEvE,kCAAmC,CACnC,mCAAoC,CACpC,uCAAwC,CACxC,iCAAoC,CACpC,kCAAmC,CAGnC,0BAA2B,CAC3B,0BAA2B,CAC3B,0BAA2B,CAC3B,2BAA4B,CAC5B,2BAA4B,CAC5B,4BAA6B,CAC7B,qCAAsC,CACtC,qCAAsC,CACtC,uCACF,CAGA,kBACE,qCAAsC,CACtC,oCAAqC,CACrC,qCAAsC,CACtC,wCAAyC,CAEzC,iCAAkC,CAClC,mCAAoC,CACpC,kCAAmC,CACnC,iCAAkC,CAElC,iCAAkC,CAClC,kCAAmC,CACnC,gCACF,CAGA,2BACE,8BAAkC,CAClC,gCAAoC,CACpC,kCAAsC,CACtC,kCAAsC,CACtC,8BAAkC,CAClC,+BAAmC,CACnC,6BAAiC,CAEjC,iCAAqC,CACrC,gCACF,CAGA,UACE,6BAA8B,CAC9B,0BAA2B,CAC3B,wBAAyB,CACzB,qBACF,CAEA,UACE,4BAA6B,CAC7B,2BAA4B,CAC5B,uBAAwB,CACxB,sBACF,CAGA,uCACE,MACE,kCAAmC,CACnC,oCAAqC,CACrC,kCAAmC,CACnC,oCAAqC,CACrC,qCAAsC,CACtC,wCAAyC,CAEzC,wCAAyC,CACzC,sCAAuC,CACvC,oCAAqC,CACrC,kCAAmC,CACnC,kCAAmC,CACnC,mCACF,CACF,CAGA,+BACE,MACE,8BAAkC,CAClC,kCAAsC,CACtC,8BAAkC,CAClC,gCACF,CACF,CAGA,mCACE,MACE,qCAAsC,CACtC,oCAAqC,CACrC,qCAAsC,CACtC,wCAAyC,CAEzC,iCAAkC,CAClC,mCAAoC,CACpC,kCAAmC,CACnC,iCAAkC,CAElC,iCAAkC,CAClC,kCAAmC,CACnC,gCACF,CACF,CAGA,WAIE,iBAAkB,CAHlB,iBAAoB,CACpB,iBAAkB,CAClB,eAAgB,CAEhB,6FACF,CAEA,WAIE,iBAAkB,CAHlB,4BAA+B,CAC/B,iBAAkB,CAClB,eAAgB,CAEhB,wGACF,CAGA,iBAGE,qBACF,CAEA,EACE,QAAS,CACT,SACF,CAEA,KACE,sBACF,CAEA,uCACE,KACE,oBACF,CACF,CAEA,KAOE,kCAAmC,CACnC,iCAAkC,CAFlC,wBAAoD,CAApD,mDAAoD,CADpD,aAAqC,CAArC,oCAAqC,CAJrC,8CAAyC,CAAzC,wCAAyC,CACzC,cAAqC,CAArC,oCAAqC,CACrC,eAAyC,CAAzC,wCAAyC,CACzC,eAA2C,CAA3C,0CAKF,CAGA,OACE,yBAA8G,CAA9G,6GAA8G,CAC9G,kBAAkD,CAAlD,iDACF,CAEA,2BACE,YACF,CAEA,eACE,yBAA8G,CAA9G,6GAA8G,CAC9G,kBAAkD,CAAlD,iDACF,CAGA,WAME,kBAA0C,CAA1C,yCAA0C,CAG1C,oBAAgD,CAAhD,+CAAgD,CAFhD,UAAqC,CAArC,oCAAqC,CAGrC,eAA2C,CAA3C,0CAA2C,CAP3C,QAAS,CAET,aAA8B,CAA9B,6BAA8B,CAJ9B,iBAAkB,CAOlB,oBAAqB,CANrB,SAAU,CAEV,YAAkC,CAAlC,iCAOF,CAEA,iBACE,OACF,CAGA,SAOE,kBAAsB,CAEtB,QAAS,CANT,UAAW,CAEX,WAAY,CACZ,eAAgB,CAFhB,SAAU,CAHV,iBAAkB,CAOlB,kBAAmB,CANnB,SAQF,CClZA,wBAIE,kBAAqC,CAArC,mCAAqC,CAErC,aAAmC,CAAnC,iCAAmC,CALnC,YAAa,CAIb,6DAAmE,CAHnE,YAAa,CAKb,eAAgB,CAJhB,WAKF,CAEA,8BACE,yBAA8C,CAA9C,4CAA8C,CAC9C,mBACF,CAGA,aAEE,YAAa,CADb,QAAO,CAEP,qBAAsB,CAEtB,WAAY,CADZ,iBAEF,CAGA,yBAKE,kBAAmB,CAHnB,kBAAqC,CAArC,mCAAqC,CACrC,+BAAqD,CAArD,mDAAqD,CACrD,YAAa,CAHb,WAAY,CAKZ,cACF,CAEA,aAIE,aAAqC,CAArC,mCAAqC,CAHrC,YAAa,CAEb,cAAe,CADf,QAGF,CAEA,kBACE,eACF,CAGA,yBACE,iBAAkB,CAElB,UAAW,CADX,QAAS,CAET,SACF,CAEA,iBACE,kBAAyC,CAAzC,uCAAyC,CAGzC,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CACf,eAAgB,CAHhB,eAIF,CAEA,kBAIE,eAA+C,CAA/C,0CAA+C,CAC/C,wBAA8C,CAA9C,4CAA8C,CAC9C,WAAY,CALZ,QAAO,CAEP,eAAgB,CADhB,iBAKF,CAEA,yBACE,eACF,CAGA,aAIE,4DAC8E,CAD9E,oFAC8E,CAC9E,yBAA0B,CAJ1B,WAAY,CACZ,iBAAkB,CAIlB,oBAAqB,CACrB,iCAAmC,CAPnC,UAQF,CAEA,yBACE,0BAAgE,CAAhE,gDACF,CAEA,iBAKE,WAAY,CAFZ,MAAO,CAGP,mBAAoB,CALpB,iBAAkB,CAClB,KAAM,CAEN,UAAW,CAGX,SACF,CAEA,iBACE,iBAAkB,CAClB,SACF,CAGA,8BAGE,eAAoC,CAApC,+BAAoC,CACpC,wBAA8C,CAA9C,4CAA8C,CAC9C,kBAAmB,CACnB,mCAAkE,CAAlE,uDAAkE,CAClE,cAAe,CAGf,eAAgB,CADhB,eAAgB,CAPhB,iBAAkB,CAMlB,uBAAyB,CAGzB,wBAAiB,CAAjB,gBACF,CAGA,gBAEE,kBAAmB,CAInB,kBAA2C,CAA3C,yCAA2C,CAD3C,+BAAqD,CAArD,mDAAqD,CAErD,2BAA4B,CAN5B,YAAa,CAEb,6BAA8B,CAC9B,YAIF,CAEA,eAIE,aAAmC,CAAnC,iCAAmC,CAFnC,cAAe,CACf,eAAgB,CAFhB,QAIF,CAEA,oBACE,kBAAyC,CAAzC,uCAAyC,CAGzC,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CACf,eAAgB,CAHhB,eAIF,CAEA,iBACE,YACF,CAEA,eACE,kBACF,CAEA,0BACE,eACF,CAEA,aAGE,aAAqC,CAArC,mCAAqC,CAFrC,cAAe,CACf,eAAgB,CAIhB,mBAAqB,CAFrB,iBAAkB,CAClB,wBAEF,CAEA,aAEE,aAAmC,CAAnC,iCAAmC,CADnC,cAEF,CAGA,kBAEE,kBAAmB,CADnB,YAAa,CAGb,cAAe,CACf,eAAgB,CAFhB,OAGF,CAEA,YAGE,iBAAkB,CADlB,UAAW,CADX,SAGF,CAEA,gCACE,kBAAoC,CAApC,kCACF,CAEA,gCACE,kBAAyC,CAAzC,uCACF,CAEA,8BACE,kBAAyC,CAAzC,uCACF,CAGA,oBAIE,iBAAkB,CAHlB,cAAe,CACf,eAAgB,CAChB,eAEF,CAEA,cACE,kBAAyC,CAAzC,uCAAyC,CACzC,aAAmC,CAAnC,iCACF,CAEA,iBACE,kBAAyC,CAAzC,uCAAyC,CACzC,aAAmC,CAAnC,iCACF,CAEA,eACE,kBAAuC,CAAvC,qCAAuC,CACvC,aAAiC,CAAjC,+BACF,CAGA,cAGE,kBAAoC,CAApC,kCAAoC,CACpC,iBAAkB,CAFlB,UAAW,CAGX,eAAgB,CAJhB,UAKF,CAEA,eAEE,kBAAyC,CAAzC,uCAAyC,CADzC,WAAY,CAEZ,yBACF,CAEA,0CAEE,oBAAgD,CAAhD,8CAAgD,CAChD,qCAA0E,CAA1E,+DAA0E,CAC1E,0BACF,CAEA,gDAEE,oBAA2C,CAA3C,yCAA2C,CAC3C,8BAA2E,CAA3E,2DACF,CAEA,gDAEE,UAAY,CACZ,sBAAuB,CACvB,YACF,CAGA,cAEE,kBAAmB,CAGnB,kBAA2C,CAA3C,yCAA2C,CAD3C,+BAAqD,CAArD,mDAAqD,CAErD,2BAA4B,CAL5B,YAAa,CAEb,YAIF,CAEA,YAIE,aAAoC,CAApC,kCAAoC,CAFpC,WAAY,CACZ,iBAAkB,CAFlB,UAIF,CAEA,aACE,QAAO,CACP,WACF,CAEA,gBACE,QAAS,CAIT,eAAgB,CAChB,sBAAuB,CACvB,kBACF,CAEA,6BANE,aAAmC,CAAnC,iCAAmC,CAFnC,cAAe,CACf,eAgBF,CATA,aAGE,gBAAuB,CAKvB,WAAsD,CAAtD,+BAAsD,CAAtD,oDAAsD,CADtD,YAAa,CANb,UAQF,CAEA,eACE,YAAa,CACb,OACF,CAEA,sBASE,kBAAmB,CAJnB,kBAAqC,CAArC,mCAAqC,CAFrC,WAAY,CACZ,iBAAkB,CAElB,aAAqC,CAArC,mCAAqC,CACrC,cAAe,CACf,YAAa,CANb,WAAY,CAQZ,sBAAuB,CACvB,uBAAyB,CAVzB,UAWF,CAEA,4BACE,kBAA2C,CAA3C,yCAA2C,CAC3C,aAAmC,CAAnC,iCACF,CAEA,UACE,4BAAoD,CAApD,iDAAoD,CACpD,oBACF,CAEA,YACE,4BAAkD,CAAlD,+CAAkD,CAClD,oBACF,CAGA,eACE,YACF,CAGA,cAME,kBAA2C,CAA3C,yCAA2C,CAC3C,2BAA4B,CAF5B,4BAAkD,CAAlD,gDAAkD,CAIlD,aAAqC,CAArC,mCAAqC,CADrC,cAAe,CANf,6BAA8B,CAE9B,iBAMF,CAEA,8BATE,kBAAmB,CAFnB,YAeF,CAJA,gBAGE,OACF,CAEA,WAEE,aAAoC,CAApC,kCAAoC,CADpC,iBAEF,CAGA,kBAIE,kBAAyC,CAAzC,uCAAyC,CACzC,qBAAqD,CAArD,gDAAqD,CACrD,iBAAkB,CAClB,gBAAiB,CAJjB,WAAY,CAKZ,SAAU,CAPV,iBAAkB,CAQlB,2BAA6B,CAP7B,UAAW,CAQX,UACF,CAEA,+EAEE,SACF,CAEA,sBAEE,QAAS,CADT,QAAS,CAET,0BACF,CAEA,wBACE,UAAW,CACX,OAAQ,CACR,0BACF,CAEA,yBACE,WAAY,CACZ,QAAS,CACT,0BACF,CAEA,uBACE,SAAU,CACV,OAAQ,CACR,0BACF,CAEA,wBACE,kBAAyC,CAAzC,uCAAyC,CACzC,oBACF,CAGA,yBACE,wBACE,qBACF,CAEA,cAEE,eAAgB,CADhB,eAEF,CAMA,6BACE,YACF,CACF,CAGA,mCACE,wBACE,mBAAoB,CACpB,6BAA8B,CAC9B,sBAAuB,CACvB,wBAAyB,CACzB,sBAAuB,CACvB,kBAAmB,CACnB,sBAAuB,CACvB,yBAA0B,CAC1B,yBAA0B,CAC1B,wBACF,CACF,CAGA,+BACE,cACE,gBACF,CAEA,uBACE,gBACF,CAEA,kBACE,gBACF,CACF,CAGA,uCACE,6CAGE,eACF,CACF", "sources": ["index.css", "App.css", "design-system/styles/variables.css", "components/styles/SmartCanvas.css"], "sourcesContent": ["body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n* {\n  box-sizing: border-box;\n}\n\nhtml, body, #root {\n  height: 100%;\n  overflow: hidden;\n}", "/* Global Styles */\n* {\n  box-sizing: border-box;\n}\n\nbody {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #f8f9fa;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n/* App Container */\n.App {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n/* Smart Canvas Container */\n.smart-canvas-container {\n  display: flex;\n  height: 100vh;\n  background-color: #f8f9fa;\n  overflow: hidden;\n}\n\n.smart-canvas-container:focus {\n  outline: none;\n}\n\n/* Canvas Main Area */\n.canvas-main {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n.canvas-workspace {\n  flex: 1;\n  position: relative;\n  overflow: hidden;\n  background-color: #ffffff;\n  background-image: \n    linear-gradient(rgba(0, 0, 0, 0.05) 1px, transparent 1px),\n    linear-gradient(90deg, rgba(0, 0, 0, 0.05) 1px, transparent 1px);\n  background-size: 20px 20px;\n  cursor: grab;\n}\n\n.canvas-workspace:active {\n  cursor: grabbing;\n}\n\n/* Component Sidebar */\n.component-sidebar {\n  width: 280px;\n  background: white;\n  border-right: 1px solid #e1e5e9;\n  display: flex;\n  flex-direction: column;\n  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.05);\n  z-index: 10;\n}\n\n.sidebar-header {\n  padding: 20px;\n  border-bottom: 1px solid #e1e5e9;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n}\n\n.sidebar-header h2 {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.sidebar-content {\n  flex: 1;\n  padding: 20px;\n  overflow-y: auto;\n}\n\n.component-category {\n  margin-bottom: 24px;\n}\n\n.category-title {\n  font-size: 14px;\n  font-weight: 600;\n  color: #495057;\n  margin-bottom: 12px;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.component-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.component-item {\n  padding: 12px 16px;\n  border: 1px solid #e1e5e9;\n  border-radius: 6px;\n  background: #f8f9fa;\n  cursor: grab;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.component-item:hover {\n  background: #e9ecef;\n  border-color: #adb5bd;\n  transform: translateY(-1px);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.component-item:active {\n  cursor: grabbing;\n  transform: translateY(0);\n}\n\n.component-icon {\n  width: 20px;\n  height: 20px;\n  color: #6c757d;\n}\n\n.component-info {\n  flex: 1;\n}\n\n.component-name {\n  font-size: 14px;\n  font-weight: 500;\n  color: #495057;\n  margin-bottom: 2px;\n}\n\n.component-description {\n  font-size: 12px;\n  color: #6c757d;\n}\n\n/* Canvas Grid */\n.canvas-grid {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n}\n\n/* Canvas Elements */\n.canvas-element {\n  position: absolute;\n  background: white;\n  border: 2px solid #e1e5e9;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  cursor: move;\n  transition: all 0.2s ease;\n  user-select: none;\n}\n\n.canvas-element:hover {\n  border-color: #007bff;\n  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);\n}\n\n.canvas-element.selected {\n  border-color: #007bff;\n  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);\n}\n\n.canvas-element.dragging {\n  z-index: 1000;\n  transform: rotate(2deg);\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);\n}\n\n/* Element Types */\n.element-smart-goal {\n  border-color: #28a745;\n  background: linear-gradient(135deg, #ffffff 0%, #f8fff9 100%);\n}\n\n.element-smart-goal.selected {\n  border-color: #28a745;\n  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.25);\n}\n\n.element-task {\n  border-color: #007bff;\n  background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);\n}\n\n.element-task.selected {\n  border-color: #007bff;\n  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);\n}\n\n.element-kpi {\n  border-color: #ffc107;\n  background: linear-gradient(135deg, #ffffff 0%, #fffdf8 100%);\n}\n\n.element-kpi.selected {\n  border-color: #ffc107;\n  box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.25);\n}\n\n.element-brand-asset {\n  border-color: #6f42c1;\n  background: linear-gradient(135deg, #ffffff 0%, #faf9ff 100%);\n}\n\n.element-brand-asset.selected {\n  border-color: #6f42c1;\n  box-shadow: 0 0 0 3px rgba(111, 66, 193, 0.25);\n}\n\n/* Element Content */\n.element-header {\n  padding: 12px 16px;\n  border-bottom: 1px solid #e1e5e9;\n  background: rgba(0, 0, 0, 0.02);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.element-title {\n  font-size: 14px;\n  font-weight: 600;\n  color: #495057;\n  margin: 0;\n}\n\n.element-type-badge {\n  font-size: 10px;\n  padding: 2px 6px;\n  border-radius: 3px;\n  background: #e9ecef;\n  color: #6c757d;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.element-content {\n  padding: 16px;\n}\n\n.element-field {\n  margin-bottom: 12px;\n}\n\n.element-field:last-child {\n  margin-bottom: 0;\n}\n\n.field-label {\n  font-size: 12px;\n  font-weight: 500;\n  color: #6c757d;\n  margin-bottom: 4px;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.field-value {\n  font-size: 14px;\n  color: #495057;\n}\n\n.field-input {\n  width: 100%;\n  padding: 6px 8px;\n  border: 1px solid #e1e5e9;\n  border-radius: 4px;\n  font-size: 14px;\n  color: #495057;\n}\n\n.field-input:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n/* Progress Bars */\n.progress-bar {\n  width: 100%;\n  height: 8px;\n  background: #e9ecef;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.progress-fill {\n  height: 100%;\n  background: linear-gradient(90deg, #28a745 0%, #20c997 100%);\n  transition: width 0.3s ease;\n}\n\n/* Status Indicators */\n.status-indicator {\n  display: inline-flex;\n  align-items: center;\n  gap: 6px;\n  font-size: 12px;\n  font-weight: 500;\n}\n\n.status-dot {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n}\n\n.status-not-started .status-dot {\n  background: #6c757d;\n}\n\n.status-in-progress .status-dot {\n  background: #007bff;\n}\n\n.status-completed .status-dot {\n  background: #28a745;\n}\n\n.status-on-hold .status-dot {\n  background: #ffc107;\n}\n\n/* Priority Indicators */\n.priority-indicator {\n  display: inline-flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 12px;\n  font-weight: 500;\n  padding: 2px 6px;\n  border-radius: 3px;\n}\n\n.priority-low {\n  background: #d1ecf1;\n  color: #0c5460;\n}\n\n.priority-medium {\n  background: #fff3cd;\n  color: #856404;\n}\n\n.priority-high {\n  background: #f8d7da;\n  color: #721c24;\n}\n\n.priority-urgent {\n  background: #d4edda;\n  color: #155724;\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% { opacity: 1; }\n  50% { opacity: 0.7; }\n  100% { opacity: 1; }\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .smart-canvas-container {\n    flex-direction: column;\n  }\n  \n  .component-sidebar {\n    width: 100%;\n    height: 200px;\n    border-right: none;\n    border-bottom: 1px solid #e1e5e9;\n  }\n  \n  .sidebar-content {\n    padding: 12px;\n  }\n  \n  .component-list {\n    flex-direction: row;\n    overflow-x: auto;\n    gap: 12px;\n  }\n  \n  .component-item {\n    min-width: 150px;\n    flex-shrink: 0;\n  }\n}\n\n/* Accessibility */\n@media (prefers-reduced-motion: reduce) {\n  * {\n    animation-duration: 0.01ms !important;\n    animation-iteration-count: 1 !important;\n    transition-duration: 0.01ms !important;\n  }\n}\n\n/* High Contrast Mode */\n@media (prefers-contrast: high) {\n  .canvas-element {\n    border-width: 3px;\n  }\n  \n  .element-header {\n    background: rgba(0, 0, 0, 0.1);\n  }\n  \n  .field-input:focus {\n    border-width: 2px;\n  }\n}\n\n/* Print Styles */\n@media print {\n  .component-sidebar,\n  .paim-fab,\n  .paim-panel {\n    display: none !important;\n  }\n  \n  .canvas-main {\n    width: 100% !important;\n  }\n  \n  .canvas-workspace {\n    background: white !important;\n  }\n  \n  .canvas-element {\n    box-shadow: none !important;\n    border: 2px solid #000 !important;\n  }\n}", "/**\n * CSS Custom Properties (Variables)\n * Generated from design tokens with accessibility and cultural support\n */\n\n:root {\n  /* Color Tokens */\n  --cove-color-primary: #2E2E2E;\n  --cove-color-primary-50: #f0f9ff;\n  --cove-color-primary-100: #e0f2fe;\n  --cove-color-primary-200: #bae6fd;\n  --cove-color-primary-300: #7dd3fc;\n  --cove-color-primary-400: #38bdf8;\n  --cove-color-primary-500: #0ea5e9;\n  --cove-color-primary-600: #0284c7;\n  --cove-color-primary-700: #0369a1;\n  --cove-color-primary-800: #075985;\n  --cove-color-primary-900: #0c4a6e;\n  --cove-color-primary-950: #082f49;\n  \n  --cove-color-accent: #5FB9FF;\n  --cove-color-accent-50: #eff6ff;\n  --cove-color-accent-100: #dbeafe;\n  --cove-color-accent-200: #bfdbfe;\n  --cove-color-accent-300: #93c5fd;\n  --cove-color-accent-400: #60a5fa;\n  --cove-color-accent-500: #3b82f6;\n  --cove-color-accent-600: #2563eb;\n  --cove-color-accent-700: #1d4ed8;\n  --cove-color-accent-800: #1e40af;\n  --cove-color-accent-900: #1e3a8a;\n  --cove-color-accent-950: #172554;\n  \n  --cove-color-warning: #F6C343;\n  --cove-color-danger: #FF6161;\n  --cove-color-critical: #D45AFF;\n  --cove-color-success: #4CC28E;\n  \n  --cove-color-background-light: #FAFAFA;\n  --cove-color-background-dark: #181818;\n  --cove-color-background-paper: #FFFFFF;\n  --cove-color-background-elevated: #F5F5F5;\n  \n  --cove-color-text-primary: #1F2937;\n  --cove-color-text-secondary: #6B7280;\n  --cove-color-text-tertiary: #9CA3AF;\n  --cove-color-text-inverse: #FFFFFF;\n  --cove-color-text-disabled: #D1D5DB;\n  \n  --cove-color-border-light: #E5E7EB;\n  --cove-color-border-medium: #D1D5DB;\n  --cove-color-border-dark: #9CA3AF;\n  --cove-color-border-focus: #3B82F6;\n  \n  /* Typography Tokens */\n  --cove-font-family-base: 'Inter', 'Satoshi', system-ui, sans-serif;\n  --cove-font-family-arabic: 'Noto Sans Arabic', 'Amiri', serif;\n  --cove-font-family-dyslexic: 'OpenDyslexic', 'Comic Sans MS', sans-serif;\n  --cove-font-family-mono: 'JetBrains Mono', 'Consolas', monospace;\n  \n  --cove-font-size-xs: 0.75rem;\n  --cove-font-size-sm: 0.875rem;\n  --cove-font-size-base: 1rem;\n  --cove-font-size-lg: 1.125rem;\n  --cove-font-size-xl: 1.25rem;\n  --cove-font-size-2xl: 1.5rem;\n  --cove-font-size-3xl: 1.875rem;\n  --cove-font-size-4xl: 2.25rem;\n  --cove-font-size-5xl: 3rem;\n  --cove-font-size-6xl: 3.75rem;\n  --cove-font-size-7xl: 4.5rem;\n  --cove-font-size-8xl: 6rem;\n  --cove-font-size-9xl: 8rem;\n  \n  --cove-font-weight-thin: 100;\n  --cove-font-weight-extralight: 200;\n  --cove-font-weight-light: 300;\n  --cove-font-weight-normal: 400;\n  --cove-font-weight-medium: 500;\n  --cove-font-weight-semibold: 600;\n  --cove-font-weight-bold: 700;\n  --cove-font-weight-extrabold: 800;\n  --cove-font-weight-black: 900;\n  --cove-font-weight-header: 600;\n  --cove-font-weight-body: 400;\n  \n  --cove-line-height-none: 1;\n  --cove-line-height-tight: 1.25;\n  --cove-line-height-snug: 1.375;\n  --cove-line-height-normal: 1.5;\n  --cove-line-height-relaxed: 1.625;\n  --cove-line-height-loose: 2;\n  \n  --cove-letter-spacing-tighter: -0.05em;\n  --cove-letter-spacing-tight: -0.025em;\n  --cove-letter-spacing-normal: 0em;\n  --cove-letter-spacing-wide: 0.025em;\n  --cove-letter-spacing-wider: 0.05em;\n  --cove-letter-spacing-widest: 0.1em;\n  \n  /* Spacing Tokens */\n  --cove-spacing-0: 0px;\n  --cove-spacing-1: 0.25rem;\n  --cove-spacing-2: 0.5rem;\n  --cove-spacing-3: 0.75rem;\n  --cove-spacing-4: 1rem;\n  --cove-spacing-5: 1.25rem;\n  --cove-spacing-6: 1.5rem;\n  --cove-spacing-8: 2rem;\n  --cove-spacing-10: 2.5rem;\n  --cove-spacing-12: 3rem;\n  --cove-spacing-16: 4rem;\n  --cove-spacing-20: 5rem;\n  --cove-spacing-24: 6rem;\n  --cove-spacing-32: 8rem;\n  --cove-spacing-40: 10rem;\n  --cove-spacing-48: 12rem;\n  --cove-spacing-56: 14rem;\n  --cove-spacing-64: 16rem;\n  \n  /* Border Radius Tokens */\n  --cove-border-radius-none: 0px;\n  --cove-border-radius-sm: 0.125rem;\n  --cove-border-radius-default: 0.25rem;\n  --cove-border-radius-md: 0.375rem;\n  --cove-border-radius-lg: 0.5rem;\n  --cove-border-radius-xl: 0.75rem;\n  --cove-border-radius-2xl: 1rem;\n  --cove-border-radius-3xl: 1.5rem;\n  --cove-border-radius-full: 9999px;\n  --cove-border-radius-button: 0.5rem;\n  --cove-border-radius-input: 1rem;\n  \n  /* Shadow Tokens */\n  --cove-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --cove-shadow-default: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --cove-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --cove-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --cove-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --cove-shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n  --cove-shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);\n  --cove-shadow-none: none;\n  \n  /* Z-Index Tokens */\n  --cove-z-index-auto: auto;\n  --cove-z-index-0: 0;\n  --cove-z-index-10: 10;\n  --cove-z-index-20: 20;\n  --cove-z-index-30: 30;\n  --cove-z-index-40: 40;\n  --cove-z-index-50: 50;\n  --cove-z-index-dropdown: 1000;\n  --cove-z-index-sticky: 1020;\n  --cove-z-index-fixed: 1030;\n  --cove-z-index-modal: 1040;\n  --cove-z-index-popover: 1050;\n  --cove-z-index-tooltip: 1060;\n  --cove-z-index-toast: 1070;\n  \n  /* Motion Tokens */\n  --cove-motion-duration-instant: 0ms;\n  --cove-motion-duration-fast: 150ms;\n  --cove-motion-duration-normal: 200ms;\n  --cove-motion-duration-slow: 300ms;\n  --cove-motion-duration-slower: 500ms;\n  --cove-motion-duration-slowest: 800ms;\n  --cove-motion-duration-glow-pulse: 3s;\n  --cove-motion-duration-reduced: 0.01ms;\n  --cove-motion-duration-respectful: 250ms;\n  \n  --cove-motion-easing-linear: linear;\n  --cove-motion-easing-ease: ease;\n  --cove-motion-easing-ease-in: ease-in;\n  --cove-motion-easing-ease-out: ease-out;\n  --cove-motion-easing-ease-in-out: ease-in-out;\n  --cove-motion-easing-slide-spring: cubic-bezier(0.4, 0, 0.2, 1);\n  --cove-motion-easing-bounce-out: cubic-bezier(0.34, 1.56, 0.64, 1);\n  --cove-motion-easing-back-out: cubic-bezier(0.34, 1.26, 0.64, 1);\n  --cove-motion-easing-gentle: cubic-bezier(0.25, 0.46, 0.45, 0.94);\n  --cove-motion-easing-smooth: cubic-bezier(0.4, 0.0, 0.2, 1);\n  --cove-motion-easing-elegant: cubic-bezier(0.25, 0.46, 0.45, 0.94);\n  \n  /* Accessibility Tokens */\n  --cove-a11y-focus-ring-width: 2px;\n  --cove-a11y-focus-ring-style: solid;\n  --cove-a11y-focus-ring-color: #3B82F6;\n  --cove-a11y-focus-ring-offset: 2px;\n  \n  --cove-a11y-touch-target-minimum: 44px;\n  --cove-a11y-touch-target-recommended: 48px;\n  --cove-a11y-touch-target-comfortable: 56px;\n  \n  /* Cultural Tokens */\n  --cove-cultural-font-arabic: 'Noto Sans Arabic', 'IBM Plex Sans Arabic', 'Tajawal', sans-serif;\n  --cove-cultural-font-traditional: 'Amiri', 'Scheherazade New', 'Noto Naskh Arabic', serif;\n  --cove-cultural-font-display: 'Cairo', 'Almarai', 'Markazi Text', serif;\n  \n  --cove-cultural-color-gold: #D4AF37;\n  --cove-cultural-color-pearl: #F8F6F0;\n  --cove-cultural-color-turquoise: #40E0D0;\n  --cove-cultural-color-coral: #FF7F50;\n  --cove-cultural-color-sand: #F4A460;\n  \n  /* Breakpoints */\n  --cove-breakpoint-xs: 320px;\n  --cove-breakpoint-sm: 640px;\n  --cove-breakpoint-md: 768px;\n  --cove-breakpoint-lg: 1024px;\n  --cove-breakpoint-xl: 1280px;\n  --cove-breakpoint-2xl: 1536px;\n  --cove-breakpoint-arabic-mobile: 375px;\n  --cove-breakpoint-arabic-tablet: 768px;\n  --cove-breakpoint-arabic-desktop: 1200px;\n}\n\n/* Dark Theme Variables */\n[data-theme=\"dark\"] {\n  --cove-color-background-light: #181818;\n  --cove-color-background-dark: #0F0F0F;\n  --cove-color-background-paper: #1F1F1F;\n  --cove-color-background-elevated: #2A2A2A;\n  \n  --cove-color-text-primary: #F9FAFB;\n  --cove-color-text-secondary: #D1D5DB;\n  --cove-color-text-tertiary: #9CA3AF;\n  --cove-color-text-inverse: #1F2937;\n  \n  --cove-color-border-light: #374151;\n  --cove-color-border-medium: #4B5563;\n  --cove-color-border-dark: #6B7280;\n}\n\n/* High Contrast Theme */\n[data-theme=\"high-contrast\"] {\n  --cove-color-text-primary: #000000;\n  --cove-color-text-secondary: #000000;\n  --cove-color-background-light: #FFFFFF;\n  --cove-color-background-paper: #FFFFFF;\n  --cove-color-border-light: #000000;\n  --cove-color-border-medium: #000000;\n  --cove-color-border-dark: #000000;\n  \n  --cove-a11y-focus-ring-color: #000000;\n  --cove-a11y-focus-ring-width: 3px;\n}\n\n/* RTL Support */\n[dir=\"rtl\"] {\n  --cove-text-align-start: right;\n  --cove-text-align-end: left;\n  --cove-float-start: right;\n  --cove-float-end: left;\n}\n\n[dir=\"ltr\"] {\n  --cove-text-align-start: left;\n  --cove-text-align-end: right;\n  --cove-float-start: left;\n  --cove-float-end: right;\n}\n\n/* Reduced Motion Support */\n@media (prefers-reduced-motion: reduce) {\n  :root {\n    --cove-motion-duration-fast: 0.01ms;\n    --cove-motion-duration-normal: 0.01ms;\n    --cove-motion-duration-slow: 0.01ms;\n    --cove-motion-duration-slower: 0.01ms;\n    --cove-motion-duration-slowest: 0.01ms;\n    --cove-motion-duration-glow-pulse: 0.01ms;\n    \n    --cove-motion-easing-slide-spring: linear;\n    --cove-motion-easing-bounce-out: linear;\n    --cove-motion-easing-back-out: linear;\n    --cove-motion-easing-gentle: linear;\n    --cove-motion-easing-smooth: linear;\n    --cove-motion-easing-elegant: linear;\n  }\n}\n\n/* High Contrast Preference */\n@media (prefers-contrast: high) {\n  :root {\n    --cove-color-text-primary: #000000;\n    --cove-color-background-light: #FFFFFF;\n    --cove-color-border-light: #000000;\n    --cove-a11y-focus-ring-width: 3px;\n  }\n}\n\n/* Dark Mode Preference */\n@media (prefers-color-scheme: dark) {\n  :root {\n    --cove-color-background-light: #181818;\n    --cove-color-background-dark: #0F0F0F;\n    --cove-color-background-paper: #1F1F1F;\n    --cove-color-background-elevated: #2A2A2A;\n    \n    --cove-color-text-primary: #F9FAFB;\n    --cove-color-text-secondary: #D1D5DB;\n    --cove-color-text-tertiary: #9CA3AF;\n    --cove-color-text-inverse: #1F2937;\n    \n    --cove-color-border-light: #374151;\n    --cove-color-border-medium: #4B5563;\n    --cove-color-border-dark: #6B7280;\n  }\n}\n\n/* Font Loading Optimization */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');\n}\n\n@font-face {\n  font-family: 'Noto Sans Arabic';\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');\n}\n\n/* Base Reset with Accessibility */\n*,\n*::before,\n*::after {\n  box-sizing: border-box;\n}\n\n* {\n  margin: 0;\n  padding: 0;\n}\n\nhtml {\n  scroll-behavior: smooth;\n}\n\n@media (prefers-reduced-motion: reduce) {\n  html {\n    scroll-behavior: auto;\n  }\n}\n\nbody {\n  font-family: var(--cove-font-family-base);\n  font-size: var(--cove-font-size-base);\n  font-weight: var(--cove-font-weight-body);\n  line-height: var(--cove-line-height-normal);\n  color: var(--cove-color-text-primary);\n  background-color: var(--cove-color-background-light);\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n/* Focus Management */\n:focus {\n  outline: var(--cove-a11y-focus-ring-width) var(--cove-a11y-focus-ring-style) var(--cove-a11y-focus-ring-color);\n  outline-offset: var(--cove-a11y-focus-ring-offset);\n}\n\n:focus:not(:focus-visible) {\n  outline: none;\n}\n\n:focus-visible {\n  outline: var(--cove-a11y-focus-ring-width) var(--cove-a11y-focus-ring-style) var(--cove-a11y-focus-ring-color);\n  outline-offset: var(--cove-a11y-focus-ring-offset);\n}\n\n/* Skip Links */\n.skip-link {\n  position: absolute;\n  top: -40px;\n  left: 6px;\n  z-index: var(--cove-z-index-toast);\n  padding: var(--cove-spacing-2);\n  background: var(--cove-color-text-primary);\n  color: var(--cove-color-text-inverse);\n  text-decoration: none;\n  border-radius: var(--cove-border-radius-default);\n  font-weight: var(--cove-font-weight-medium);\n}\n\n.skip-link:focus {\n  top: 6px;\n}\n\n/* Screen Reader Only */\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border: 0;\n}", "/* SMART Canvas Main Container */\n.smart-canvas-container {\n  display: flex;\n  height: 100vh;\n  width: 100vw;\n  background: var(--canvas-bg, #f8f9fa);\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n  color: var(--text-primary, #2d3748);\n  overflow: hidden;\n}\n\n.smart-canvas-container:focus {\n  outline: 2px solid var(--focus-color, #4299e1);\n  outline-offset: -2px;\n}\n\n/* Canvas Main Area */\n.canvas-main {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  min-width: 0;\n}\n\n/* Context Bar Placeholder */\n.context-bar-placeholder {\n  height: 60px;\n  background: var(--canvas-bg, #f8f9fa);\n  border-bottom: 1px solid var(--border-color, #e2e8f0);\n  display: flex;\n  align-items: center;\n  padding: 0 20px;\n}\n\n.canvas-info {\n  display: flex;\n  gap: 20px;\n  font-size: 14px;\n  color: var(--text-secondary, #718096);\n}\n\n.canvas-info span {\n  font-weight: 500;\n}\n\n/* Connections Placeholder */\n.connections-placeholder {\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  z-index: 5;\n}\n\n.connection-info {\n  background: var(--primary-color, #4299e1);\n  color: white;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 12px;\n  font-weight: 500;\n}\n\n.canvas-workspace {\n  flex: 1;\n  position: relative;\n  overflow: hidden;\n  background: var(--canvas-workspace-bg, #ffffff);\n  border: 1px solid var(--border-color, #e2e8f0);\n  cursor: grab;\n}\n\n.canvas-workspace:active {\n  cursor: grabbing;\n}\n\n/* Canvas Grid */\n.canvas-grid {\n  width: 100%;\n  height: 100%;\n  position: relative;\n  background-image: \n    radial-gradient(circle, var(--grid-dot-color, #cbd5e0) 1px, transparent 1px);\n  background-size: 20px 20px;\n  transform-origin: 0 0;\n  transition: transform 0.1s ease-out;\n}\n\n.canvas-grid.drop-target {\n  background-color: var(--drop-target-bg, rgba(66, 153, 225, 0.1));\n}\n\n.grid-background {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n  z-index: 0;\n}\n\n.canvas-elements {\n  position: relative;\n  z-index: 1;\n}\n\n/* Canvas Block Base Styles */\n.canvas-block,\n.canvas-element {\n  position: absolute;\n  background: var(--block-bg, #ffffff);\n  border: 2px solid var(--block-border, #e2e8f0);\n  border-radius: 12px;\n  box-shadow: var(--block-shadow, 0 4px 6px -1px rgba(0, 0, 0, 0.1));\n  cursor: pointer;\n  transition: all 0.2s ease;\n  min-width: 280px;\n  max-width: 400px;\n  user-select: none;\n}\n\n/* Element specific styles */\n.element-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 16px;\n  border-bottom: 1px solid var(--border-color, #e2e8f0);\n  background: var(--block-header-bg, #f7fafc);\n  border-radius: 10px 10px 0 0;\n}\n\n.element-title {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: var(--text-primary, #2d3748);\n}\n\n.element-type-badge {\n  background: var(--primary-color, #4299e1);\n  color: white;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 12px;\n  font-weight: 500;\n}\n\n.element-content {\n  padding: 16px;\n}\n\n.element-field {\n  margin-bottom: 12px;\n}\n\n.element-field:last-child {\n  margin-bottom: 0;\n}\n\n.field-label {\n  font-size: 12px;\n  font-weight: 500;\n  color: var(--text-secondary, #718096);\n  margin-bottom: 4px;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.field-value {\n  font-size: 14px;\n  color: var(--text-primary, #2d3748);\n}\n\n/* Status indicators */\n.status-indicator {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-size: 12px;\n  font-weight: 500;\n}\n\n.status-dot {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n}\n\n.status-not-started .status-dot {\n  background: var(--gray-400, #a0aec0);\n}\n\n.status-in-progress .status-dot {\n  background: var(--warning-color, #ed8936);\n}\n\n.status-completed .status-dot {\n  background: var(--success-color, #48bb78);\n}\n\n/* Priority indicators */\n.priority-indicator {\n  font-size: 12px;\n  font-weight: 500;\n  padding: 2px 6px;\n  border-radius: 3px;\n}\n\n.priority-low {\n  background: var(--success-light, #c6f6d5);\n  color: var(--success-dark, #22543d);\n}\n\n.priority-medium {\n  background: var(--warning-light, #faf089);\n  color: var(--warning-dark, #744210);\n}\n\n.priority-high {\n  background: var(--error-light, #fed7d7);\n  color: var(--error-dark, #742a2a);\n}\n\n/* Progress bar */\n.progress-bar {\n  width: 100%;\n  height: 8px;\n  background: var(--gray-200, #edf2f7);\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.progress-fill {\n  height: 100%;\n  background: var(--primary-color, #4299e1);\n  transition: width 0.3s ease;\n}\n\n.canvas-block:hover,\n.canvas-element:hover {\n  border-color: var(--block-border-hover, #cbd5e0);\n  box-shadow: var(--block-shadow-hover, 0 10px 15px -3px rgba(0, 0, 0, 0.1));\n  transform: translateY(-2px);\n}\n\n.canvas-block.selected,\n.canvas-element.selected {\n  border-color: var(--primary-color, #4299e1);\n  box-shadow: var(--block-shadow-selected, 0 0 0 3px rgba(66, 153, 225, 0.1));\n}\n\n.canvas-block.dragging,\n.canvas-element.dragging {\n  opacity: 0.8;\n  transform: rotate(5deg);\n  z-index: 1000;\n}\n\n/* Block Header */\n.block-header {\n  display: flex;\n  align-items: center;\n  padding: 16px;\n  border-bottom: 1px solid var(--border-color, #e2e8f0);\n  background: var(--block-header-bg, #f7fafc);\n  border-radius: 10px 10px 0 0;\n}\n\n.block-icon {\n  width: 24px;\n  height: 24px;\n  margin-right: 12px;\n  color: var(--primary-color, #4299e1);\n}\n\n.block-title {\n  flex: 1;\n  min-width: 0;\n}\n\n.block-title h3 {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: var(--text-primary, #2d3748);\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.title-input {\n  width: 100%;\n  border: none;\n  background: transparent;\n  font-size: 16px;\n  font-weight: 600;\n  color: var(--text-primary, #2d3748);\n  outline: none;\n  border-bottom: 2px solid var(--primary-color, #4299e1);\n}\n\n.block-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.block-actions button {\n  width: 32px;\n  height: 32px;\n  border: none;\n  border-radius: 6px;\n  background: var(--button-bg, #edf2f7);\n  color: var(--text-secondary, #4a5568);\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.2s ease;\n}\n\n.block-actions button:hover {\n  background: var(--button-bg-hover, #e2e8f0);\n  color: var(--text-primary, #2d3748);\n}\n\n.save-btn {\n  background: var(--success-color, #48bb78) !important;\n  color: white !important;\n}\n\n.cancel-btn {\n  background: var(--error-color, #f56565) !important;\n  color: white !important;\n}\n\n/* Block Content */\n.block-content {\n  padding: 16px;\n}\n\n/* Block Footer */\n.block-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 16px;\n  border-top: 1px solid var(--border-color, #e2e8f0);\n  background: var(--block-footer-bg, #f7fafc);\n  border-radius: 0 0 10px 10px;\n  font-size: 12px;\n  color: var(--text-secondary, #718096);\n}\n\n.paim-indicator {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n}\n\n.paim-hint {\n  font-style: italic;\n  color: var(--primary-color, #4299e1);\n}\n\n/* Connection Points */\n.connection-point {\n  position: absolute;\n  width: 12px;\n  height: 12px;\n  background: var(--primary-color, #4299e1);\n  border: 2px solid var(--canvas-workspace-bg, #ffffff);\n  border-radius: 50%;\n  cursor: crosshair;\n  opacity: 0;\n  transition: opacity 0.2s ease;\n  z-index: 10;\n}\n\n.canvas-block:hover .connection-point,\n.canvas-block.selected .connection-point {\n  opacity: 1;\n}\n\n.connection-point.top {\n  top: -6px;\n  left: 50%;\n  transform: translateX(-50%);\n}\n\n.connection-point.right {\n  right: -6px;\n  top: 50%;\n  transform: translateY(-50%);\n}\n\n.connection-point.bottom {\n  bottom: -6px;\n  left: 50%;\n  transform: translateX(-50%);\n}\n\n.connection-point.left {\n  left: -6px;\n  top: 50%;\n  transform: translateY(-50%);\n}\n\n.connection-point:hover {\n  background: var(--success-color, #48bb78);\n  transform: scale(1.2);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .smart-canvas-container {\n    flex-direction: column;\n  }\n  \n  .canvas-block {\n    min-width: 240px;\n    max-width: 320px;\n  }\n  \n  .block-header {\n    padding: 12px;\n  }\n  \n  .block-content {\n    padding: 12px;\n  }\n}\n\n/* Dark Mode Support */\n@media (prefers-color-scheme: dark) {\n  .smart-canvas-container {\n    --canvas-bg: #1a202c;\n    --canvas-workspace-bg: #2d3748;\n    --text-primary: #f7fafc;\n    --text-secondary: #a0aec0;\n    --border-color: #4a5568;\n    --block-bg: #2d3748;\n    --block-border: #4a5568;\n    --block-header-bg: #1a202c;\n    --block-footer-bg: #1a202c;\n    --grid-dot-color: #4a5568;\n  }\n}\n\n/* High Contrast Mode */\n@media (prefers-contrast: high) {\n  .canvas-block {\n    border-width: 3px;\n  }\n  \n  .canvas-block.selected {\n    border-width: 4px;\n  }\n  \n  .connection-point {\n    border-width: 3px;\n  }\n}\n\n/* Reduced Motion */\n@media (prefers-reduced-motion: reduce) {\n  .canvas-block,\n  .connection-point,\n  .canvas-grid {\n    transition: none;\n  }\n}"], "names": [], "sourceRoot": ""}