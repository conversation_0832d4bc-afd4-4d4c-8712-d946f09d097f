/*! For license information please see main.474a3b17.js.LICENSE.txt */
(()=>{var e={43:(e,t,n)=>{"use strict";e.exports=n(202)},122:e=>{"use strict";e.exports=function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){if(t.constructor!==n.constructor)return!1;var r,a,o;if(Array.isArray(t)){if((r=t.length)!=n.length)return!1;for(a=r;0!==a--;)if(!e(t[a],n[a]))return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if((r=(o=Object.keys(t)).length)!==Object.keys(n).length)return!1;for(a=r;0!==a--;)if(!Object.prototype.hasOwnProperty.call(n,o[a]))return!1;for(a=r;0!==a--;){var i=o[a];if(!e(t[i],n[i]))return!1}return!0}return t!==t&&n!==n}},153:(e,t,n)=>{"use strict";var r=n(43),a=Symbol.for("react.element"),o=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,l=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,n){var r,o={},c=null,u=null;for(r in void 0!==n&&(c=""+n),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)i.call(t,r)&&!s.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===o[r]&&(o[r]=t[r]);return{$$typeof:a,type:e,key:c,ref:u,props:o,_owner:l.current}}t.Fragment=o,t.jsx=c,t.jsxs=c},202:(e,t)=>{"use strict";var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),l=Symbol.for("react.provider"),s=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var g={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},h=Object.assign,m={};function v(e,t,n){this.props=e,this.context=t,this.refs=m,this.updater=n||g}function y(){}function b(e,t,n){this.props=e,this.context=t,this.refs=m,this.updater=n||g}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=v.prototype;var x=b.prototype=new y;x.constructor=b,h(x,v.prototype),x.isPureReactComponent=!0;var w=Array.isArray,S=Object.prototype.hasOwnProperty,k={current:null},C={key:!0,ref:!0,__self:!0,__source:!0};function E(e,t,r){var a,o={},i=null,l=null;if(null!=t)for(a in void 0!==t.ref&&(l=t.ref),void 0!==t.key&&(i=""+t.key),t)S.call(t,a)&&!C.hasOwnProperty(a)&&(o[a]=t[a]);var s=arguments.length-2;if(1===s)o.children=r;else if(1<s){for(var c=Array(s),u=0;u<s;u++)c[u]=arguments[u+2];o.children=c}if(e&&e.defaultProps)for(a in s=e.defaultProps)void 0===o[a]&&(o[a]=s[a]);return{$$typeof:n,type:e,key:i,ref:l,props:o,_owner:k.current}}function T(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var D=/\/+/g;function O(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function j(e,t,a,o,i){var l=typeof e;"undefined"!==l&&"boolean"!==l||(e=null);var s=!1;if(null===e)s=!0;else switch(l){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case n:case r:s=!0}}if(s)return i=i(s=e),e=""===o?"."+O(s,0):o,w(i)?(a="",null!=e&&(a=e.replace(D,"$&/")+"/"),j(i,t,a,"",(function(e){return e}))):null!=i&&(T(i)&&(i=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(i,a+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(D,"$&/")+"/")+e)),t.push(i)),1;if(s=0,o=""===o?".":o+":",w(e))for(var c=0;c<e.length;c++){var u=o+O(l=e[c],c);s+=j(l,t,a,u,i)}else if(u=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"===typeof u)for(e=u.call(e),c=0;!(l=e.next()).done;)s+=j(l=l.value,t,a,u=o+O(l,c++),i);else if("object"===l)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function N(e,t,n){if(null==e)return e;var r=[],a=0;return j(e,r,"","",(function(e){return t.call(n,e,a++)})),r}function I(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var _={current:null},P={transition:null},z={ReactCurrentDispatcher:_,ReactCurrentBatchConfig:P,ReactCurrentOwner:k};function F(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:N,forEach:function(e,t,n){N(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return N(e,(function(){t++})),t},toArray:function(e){return N(e,(function(e){return e}))||[]},only:function(e){if(!T(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=v,t.Fragment=a,t.Profiler=i,t.PureComponent=b,t.StrictMode=o,t.Suspense=u,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=z,t.act=F,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=h({},e.props),o=e.key,i=e.ref,l=e._owner;if(null!=t){if(void 0!==t.ref&&(i=t.ref,l=k.current),void 0!==t.key&&(o=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(c in t)S.call(t,c)&&!C.hasOwnProperty(c)&&(a[c]=void 0===t[c]&&void 0!==s?s[c]:t[c])}var c=arguments.length-2;if(1===c)a.children=r;else if(1<c){s=Array(c);for(var u=0;u<c;u++)s[u]=arguments[u+2];a.children=s}return{$$typeof:n,type:e.type,key:o,ref:i,props:a,_owner:l}},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:l,_context:e},e.Consumer=e},t.createElement=E,t.createFactory=function(e){var t=E.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=T,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:I}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=P.transition;P.transition={};try{e()}finally{P.transition=t}},t.unstable_act=F,t.useCallback=function(e,t){return _.current.useCallback(e,t)},t.useContext=function(e){return _.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return _.current.useDeferredValue(e)},t.useEffect=function(e,t){return _.current.useEffect(e,t)},t.useId=function(){return _.current.useId()},t.useImperativeHandle=function(e,t,n){return _.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return _.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return _.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return _.current.useMemo(e,t)},t.useReducer=function(e,t,n){return _.current.useReducer(e,t,n)},t.useRef=function(e){return _.current.useRef(e)},t.useState=function(e){return _.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return _.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return _.current.useTransition()},t.version="18.3.1"},234:(e,t)=>{"use strict";function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<o(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,i=a>>>1;r<i;){var l=2*(r+1)-1,s=e[l],c=l+1,u=e[c];if(0>o(s,n))c<a&&0>o(u,s)?(e[r]=u,e[c]=n,r=c):(e[r]=s,e[l]=n,r=l);else{if(!(c<a&&0>o(u,n)))break e;e[r]=u,e[c]=n,r=c}}}return t}function o(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var i=performance;t.unstable_now=function(){return i.now()}}else{var l=Date,s=l.now();t.unstable_now=function(){return l.now()-s}}var c=[],u=[],d=1,f=null,p=3,g=!1,h=!1,m=!1,v="function"===typeof setTimeout?setTimeout:null,y="function"===typeof clearTimeout?clearTimeout:null,b="undefined"!==typeof setImmediate?setImmediate:null;function x(e){for(var t=r(u);null!==t;){if(null===t.callback)a(u);else{if(!(t.startTime<=e))break;a(u),t.sortIndex=t.expirationTime,n(c,t)}t=r(u)}}function w(e){if(m=!1,x(e),!h)if(null!==r(c))h=!0,P(S);else{var t=r(u);null!==t&&z(w,t.startTime-e)}}function S(e,n){h=!1,m&&(m=!1,y(T),T=-1),g=!0;var o=p;try{for(x(n),f=r(c);null!==f&&(!(f.expirationTime>n)||e&&!j());){var i=f.callback;if("function"===typeof i){f.callback=null,p=f.priorityLevel;var l=i(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof l?f.callback=l:f===r(c)&&a(c),x(n)}else a(c);f=r(c)}if(null!==f)var s=!0;else{var d=r(u);null!==d&&z(w,d.startTime-n),s=!1}return s}finally{f=null,p=o,g=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var k,C=!1,E=null,T=-1,D=5,O=-1;function j(){return!(t.unstable_now()-O<D)}function N(){if(null!==E){var e=t.unstable_now();O=e;var n=!0;try{n=E(!0,e)}finally{n?k():(C=!1,E=null)}}else C=!1}if("function"===typeof b)k=function(){b(N)};else if("undefined"!==typeof MessageChannel){var I=new MessageChannel,_=I.port2;I.port1.onmessage=N,k=function(){_.postMessage(null)}}else k=function(){v(N,0)};function P(e){E=e,C||(C=!0,k())}function z(e,n){T=v((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){h||g||(h=!0,P(S))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):D=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return r(c)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,a,o){var i=t.unstable_now();switch("object"===typeof o&&null!==o?o="number"===typeof(o=o.delay)&&0<o?i+o:i:o=i,e){case 1:var l=-1;break;case 2:l=250;break;case 5:l=1073741823;break;case 4:l=1e4;break;default:l=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:o,expirationTime:l=o+l,sortIndex:-1},o>i?(e.sortIndex=o,n(u,e),null===r(c)&&e===r(u)&&(m?(y(T),T=-1):m=!0,z(w,o-i))):(e.sortIndex=l,n(c,e),h||g||(h=!0,P(S))),e},t.unstable_shouldYield=j,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},330:(e,t,n)=>{"use strict";var r=n(43);var a="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t},o=r.useState,i=r.useEffect,l=r.useLayoutEffect,s=r.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!a(e,n)}catch(r){return!0}}var u="undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=o({inst:{value:n,getSnapshot:t}}),a=r[0].inst,u=r[1];return l((function(){a.value=n,a.getSnapshot=t,c(a)&&u({inst:a})}),[e,n,t]),i((function(){return c(a)&&u({inst:a}),e((function(){c(a)&&u({inst:a})}))}),[e]),s(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:u},391:(e,t,n)=>{"use strict";var r=n(950);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},443:(e,t,n)=>{"use strict";e.exports=n(717)},461:(e,t,n)=>{"use strict";e.exports=n(330)},579:(e,t,n)=>{"use strict";e.exports=n(153)},717:(e,t,n)=>{"use strict";var r=n(43),a=n(461);var o="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t},i=a.useSyncExternalStore,l=r.useRef,s=r.useEffect,c=r.useMemo,u=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,a){var d=l(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;d=c((function(){function e(e){if(!s){if(s=!0,i=e,e=r(e),void 0!==a&&f.hasValue){var t=f.value;if(a(t,e))return l=t}return l=e}if(t=l,o(i,e))return t;var n=r(e);return void 0!==a&&a(t,n)?(i=e,t):(i=e,l=n)}var i,l,s=!1,c=void 0===n?null:n;return[function(){return e(t())},null===c?void 0:function(){return e(c())}]}),[t,n,r,a]);var p=i(e,d[0],d[1]);return s((function(){f.hasValue=!0,f.value=p}),[p]),u(p),p}},730:(e,t,n)=>{"use strict";var r=n(43),a=n(853);function o(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=new Set,l={};function s(e,t){c(e,t),c(e+"Capture",t)}function c(e,t){for(l[e]=t,e=0;e<t.length;e++)i.add(t[e])}var u=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,p={},g={};function h(e,t,n,r,a,o,i){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=i}var m={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){m[e]=new h(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];m[t]=new h(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){m[e]=new h(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){m[e]=new h(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){m[e]=new h(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){m[e]=new h(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){m[e]=new h(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){m[e]=new h(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){m[e]=new h(e,5,!1,e.toLowerCase(),null,!1,!1)}));var v=/[\-:]([a-z])/g;function y(e){return e[1].toUpperCase()}function b(e,t,n,r){var a=m.hasOwnProperty(t)?m[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!d.call(g,e)||!d.call(p,e)&&(f.test(e)?g[e]=!0:(p[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(v,y);m[t]=new h(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(v,y);m[t]=new h(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(v,y);m[t]=new h(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){m[e]=new h(e,1,!1,e.toLowerCase(),null,!1,!1)})),m.xlinkHref=new h("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){m[e]=new h(e,1,!1,e.toLowerCase(),null,!0,!0)}));var x=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,w=Symbol.for("react.element"),S=Symbol.for("react.portal"),k=Symbol.for("react.fragment"),C=Symbol.for("react.strict_mode"),E=Symbol.for("react.profiler"),T=Symbol.for("react.provider"),D=Symbol.for("react.context"),O=Symbol.for("react.forward_ref"),j=Symbol.for("react.suspense"),N=Symbol.for("react.suspense_list"),I=Symbol.for("react.memo"),_=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var P=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var z=Symbol.iterator;function F(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=z&&e[z]||e["@@iterator"])?e:null}var L,R=Object.assign;function M(e){if(void 0===L)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);L=t&&t[1]||""}return"\n"+L+e}var A=!1;function H(e,t){if(!e||A)return"";A=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&"string"===typeof c.stack){for(var a=c.stack.split("\n"),o=r.stack.split("\n"),i=a.length-1,l=o.length-1;1<=i&&0<=l&&a[i]!==o[l];)l--;for(;1<=i&&0<=l;i--,l--)if(a[i]!==o[l]){if(1!==i||1!==l)do{if(i--,0>--l||a[i]!==o[l]){var s="\n"+a[i].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}}while(1<=i&&0<=l);break}}}finally{A=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?M(e):""}function U(e){switch(e.tag){case 5:return M(e.type);case 16:return M("Lazy");case 13:return M("Suspense");case 19:return M("SuspenseList");case 0:case 2:case 15:return e=H(e.type,!1);case 11:return e=H(e.type.render,!1);case 1:return e=H(e.type,!0);default:return""}}function B(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case k:return"Fragment";case S:return"Portal";case E:return"Profiler";case C:return"StrictMode";case j:return"Suspense";case N:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case D:return(e.displayName||"Context")+".Consumer";case T:return(e._context.displayName||"Context")+".Provider";case O:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case I:return null!==(t=e.displayName||null)?t:B(e.type)||"Memo";case _:t=e._payload,e=e._init;try{return B(e(t))}catch(n){}}return null}function W(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return B(t);case 8:return t===C?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function V(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function $(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function Y(e){e._valueTracker||(e._valueTracker=function(e){var t=$(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,o.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function Q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=$(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function q(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function K(e,t){var n=t.checked;return R({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function X(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=V(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function G(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function Z(e,t){G(e,t);var n=V(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,V(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function J(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&q(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+V(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(o(91));return R({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ae(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(o(92));if(te(n)){if(1<n.length)throw Error(o(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:V(n)}}function oe(e,t){var n=V(t.value),r=V(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function ie(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function le(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function se(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?le(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ce,ue,de=(ue=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ce=ce||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ce.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction((function(){return ue(e,t)}))}:ue);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var pe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},ge=["Webkit","ms","Moz","O"];function he(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||pe.hasOwnProperty(e)&&pe[e]?(""+t).trim():t+"px"}function me(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=he(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(pe).forEach((function(e){ge.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pe[t]=pe[e]}))}));var ve=R({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ye(e,t){if(t){if(ve[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(o(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(o(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(o(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(o(62))}}function be(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var xe=null;function we(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Se=null,ke=null,Ce=null;function Ee(e){if(e=ba(e)){if("function"!==typeof Se)throw Error(o(280));var t=e.stateNode;t&&(t=wa(t),Se(e.stateNode,e.type,t))}}function Te(e){ke?Ce?Ce.push(e):Ce=[e]:ke=e}function De(){if(ke){var e=ke,t=Ce;if(Ce=ke=null,Ee(e),t)for(e=0;e<t.length;e++)Ee(t[e])}}function Oe(e,t){return e(t)}function je(){}var Ne=!1;function Ie(e,t,n){if(Ne)return e(t,n);Ne=!0;try{return Oe(e,t,n)}finally{Ne=!1,(null!==ke||null!==Ce)&&(je(),De())}}function _e(e,t){var n=e.stateNode;if(null===n)return null;var r=wa(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(o(231,t,typeof n));return n}var Pe=!1;if(u)try{var ze={};Object.defineProperty(ze,"passive",{get:function(){Pe=!0}}),window.addEventListener("test",ze,ze),window.removeEventListener("test",ze,ze)}catch(ue){Pe=!1}function Fe(e,t,n,r,a,o,i,l,s){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(u){this.onError(u)}}var Le=!1,Re=null,Me=!1,Ae=null,He={onError:function(e){Le=!0,Re=e}};function Ue(e,t,n,r,a,o,i,l,s){Le=!1,Re=null,Fe.apply(He,arguments)}function Be(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function We(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function Ve(e){if(Be(e)!==e)throw Error(o(188))}function $e(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=Be(e)))throw Error(o(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var i=a.alternate;if(null===i){if(null!==(r=a.return)){n=r;continue}break}if(a.child===i.child){for(i=a.child;i;){if(i===n)return Ve(a),e;if(i===r)return Ve(a),t;i=i.sibling}throw Error(o(188))}if(n.return!==r.return)n=a,r=i;else{for(var l=!1,s=a.child;s;){if(s===n){l=!0,n=a,r=i;break}if(s===r){l=!0,r=a,n=i;break}s=s.sibling}if(!l){for(s=i.child;s;){if(s===n){l=!0,n=i,r=a;break}if(s===r){l=!0,r=i,n=a;break}s=s.sibling}if(!l)throw Error(o(189))}}if(n.alternate!==r)throw Error(o(190))}if(3!==n.tag)throw Error(o(188));return n.stateNode.current===n?e:t}(e))?Ye(e):null}function Ye(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Ye(e);if(null!==t)return t;e=e.sibling}return null}var Qe=a.unstable_scheduleCallback,qe=a.unstable_cancelCallback,Ke=a.unstable_shouldYield,Xe=a.unstable_requestPaint,Ge=a.unstable_now,Ze=a.unstable_getCurrentPriorityLevel,Je=a.unstable_ImmediatePriority,et=a.unstable_UserBlockingPriority,tt=a.unstable_NormalPriority,nt=a.unstable_LowPriority,rt=a.unstable_IdlePriority,at=null,ot=null;var it=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(lt(e)/st|0)|0},lt=Math.log,st=Math.LN2;var ct=64,ut=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,o=e.pingedLanes,i=268435455&n;if(0!==i){var l=i&~a;0!==l?r=dt(l):0!==(o&=i)&&(r=dt(o))}else 0!==(i=n&~a)?r=dt(i):0!==o&&(r=dt(o));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&a)&&((a=r&-r)>=(o=t&-t)||16===a&&0!==(4194240&o)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-it(t)),r|=e[n],t&=~a;return r}function pt(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function gt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function ht(){var e=ct;return 0===(4194240&(ct<<=1))&&(ct=64),e}function mt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function vt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-it(t)]=n}function yt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-it(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var bt=0;function xt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var wt,St,kt,Ct,Et,Tt=!1,Dt=[],Ot=null,jt=null,Nt=null,It=new Map,_t=new Map,Pt=[],zt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ft(e,t){switch(e){case"focusin":case"focusout":Ot=null;break;case"dragenter":case"dragleave":jt=null;break;case"mouseover":case"mouseout":Nt=null;break;case"pointerover":case"pointerout":It.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":_t.delete(t.pointerId)}}function Lt(e,t,n,r,a,o){return null===e||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[a]},null!==t&&(null!==(t=ba(t))&&St(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function Rt(e){var t=ya(e.target);if(null!==t){var n=Be(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=We(n)))return e.blockedOn=t,void Et(e.priority,(function(){kt(n)}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Mt(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Kt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=ba(n))&&St(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);xe=r,n.target.dispatchEvent(r),xe=null,t.shift()}return!0}function At(e,t,n){Mt(e)&&n.delete(t)}function Ht(){Tt=!1,null!==Ot&&Mt(Ot)&&(Ot=null),null!==jt&&Mt(jt)&&(jt=null),null!==Nt&&Mt(Nt)&&(Nt=null),It.forEach(At),_t.forEach(At)}function Ut(e,t){e.blockedOn===t&&(e.blockedOn=null,Tt||(Tt=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Ht)))}function Bt(e){function t(t){return Ut(t,e)}if(0<Dt.length){Ut(Dt[0],e);for(var n=1;n<Dt.length;n++){var r=Dt[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Ot&&Ut(Ot,e),null!==jt&&Ut(jt,e),null!==Nt&&Ut(Nt,e),It.forEach(t),_t.forEach(t),n=0;n<Pt.length;n++)(r=Pt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Pt.length&&null===(n=Pt[0]).blockedOn;)Rt(n),null===n.blockedOn&&Pt.shift()}var Wt=x.ReactCurrentBatchConfig,Vt=!0;function $t(e,t,n,r){var a=bt,o=Wt.transition;Wt.transition=null;try{bt=1,Qt(e,t,n,r)}finally{bt=a,Wt.transition=o}}function Yt(e,t,n,r){var a=bt,o=Wt.transition;Wt.transition=null;try{bt=4,Qt(e,t,n,r)}finally{bt=a,Wt.transition=o}}function Qt(e,t,n,r){if(Vt){var a=Kt(e,t,n,r);if(null===a)Vr(e,t,r,qt,n),Ft(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return Ot=Lt(Ot,e,t,n,r,a),!0;case"dragenter":return jt=Lt(jt,e,t,n,r,a),!0;case"mouseover":return Nt=Lt(Nt,e,t,n,r,a),!0;case"pointerover":var o=a.pointerId;return It.set(o,Lt(It.get(o)||null,e,t,n,r,a)),!0;case"gotpointercapture":return o=a.pointerId,_t.set(o,Lt(_t.get(o)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(Ft(e,r),4&t&&-1<zt.indexOf(e)){for(;null!==a;){var o=ba(a);if(null!==o&&wt(o),null===(o=Kt(e,t,n,r))&&Vr(e,t,r,qt,n),o===a)break;a=o}null!==a&&r.stopPropagation()}else Vr(e,t,r,null,n)}}var qt=null;function Kt(e,t,n,r){if(qt=null,null!==(e=ya(e=we(r))))if(null===(t=Be(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=We(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return qt=e,null}function Xt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ze()){case Je:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Gt=null,Zt=null,Jt=null;function en(){if(Jt)return Jt;var e,t,n=Zt,r=n.length,a="value"in Gt?Gt.value:Gt.textContent,o=a.length;for(e=0;e<r&&n[e]===a[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===a[o-t];t++);return Jt=a.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function an(e){function t(t,n,r,a,o){for(var i in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=o,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(a):a[i]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return R(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var on,ln,sn,cn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},un=an(cn),dn=R({},cn,{view:0,detail:0}),fn=an(dn),pn=R({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:En,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==sn&&(sn&&"mousemove"===e.type?(on=e.screenX-sn.screenX,ln=e.screenY-sn.screenY):ln=on=0,sn=e),on)},movementY:function(e){return"movementY"in e?e.movementY:ln}}),gn=an(pn),hn=an(R({},pn,{dataTransfer:0})),mn=an(R({},dn,{relatedTarget:0})),vn=an(R({},cn,{animationName:0,elapsedTime:0,pseudoElement:0})),yn=R({},cn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),bn=an(yn),xn=an(R({},cn,{data:0})),wn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Sn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},kn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Cn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=kn[e])&&!!t[e]}function En(){return Cn}var Tn=R({},dn,{key:function(e){if(e.key){var t=wn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?Sn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:En,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Dn=an(Tn),On=an(R({},pn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),jn=an(R({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:En})),Nn=an(R({},cn,{propertyName:0,elapsedTime:0,pseudoElement:0})),In=R({},pn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),_n=an(In),Pn=[9,13,27,32],zn=u&&"CompositionEvent"in window,Fn=null;u&&"documentMode"in document&&(Fn=document.documentMode);var Ln=u&&"TextEvent"in window&&!Fn,Rn=u&&(!zn||Fn&&8<Fn&&11>=Fn),Mn=String.fromCharCode(32),An=!1;function Hn(e,t){switch(e){case"keyup":return-1!==Pn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Un(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Bn=!1;var Wn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Vn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Wn[e.type]:"textarea"===t}function $n(e,t,n,r){Te(r),0<(t=Yr(t,"onChange")).length&&(n=new un("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Yn=null,Qn=null;function qn(e){Mr(e,0)}function Kn(e){if(Q(xa(e)))return e}function Xn(e,t){if("change"===e)return t}var Gn=!1;if(u){var Zn;if(u){var Jn="oninput"in document;if(!Jn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Jn="function"===typeof er.oninput}Zn=Jn}else Zn=!1;Gn=Zn&&(!document.documentMode||9<document.documentMode)}function tr(){Yn&&(Yn.detachEvent("onpropertychange",nr),Qn=Yn=null)}function nr(e){if("value"===e.propertyName&&Kn(Qn)){var t=[];$n(t,Qn,e,we(e)),Ie(qn,t)}}function rr(e,t,n){"focusin"===e?(tr(),Qn=n,(Yn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function ar(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Kn(Qn)}function or(e,t){if("click"===e)return Kn(t)}function ir(e,t){if("input"===e||"change"===e)return Kn(t)}var lr="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function sr(e,t){if(lr(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!d.call(t,a)||!lr(e[a],t[a]))return!1}return!0}function cr(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ur(e,t){var n,r=cr(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=cr(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=q();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=q((e=t.contentWindow).document)}return t}function pr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function gr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&pr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,o=Math.min(r.start,a);r=void 0===r.end?o:Math.min(r.end,a),!e.extend&&o>r&&(a=r,r=o,o=a),a=ur(n,o);var i=ur(n,r);a&&i&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var hr=u&&"documentMode"in document&&11>=document.documentMode,mr=null,vr=null,yr=null,br=!1;function xr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;br||null==mr||mr!==q(r)||("selectionStart"in(r=mr)&&pr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},yr&&sr(yr,r)||(yr=r,0<(r=Yr(vr,"onSelect")).length&&(t=new un("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=mr)))}function wr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Sr={animationend:wr("Animation","AnimationEnd"),animationiteration:wr("Animation","AnimationIteration"),animationstart:wr("Animation","AnimationStart"),transitionend:wr("Transition","TransitionEnd")},kr={},Cr={};function Er(e){if(kr[e])return kr[e];if(!Sr[e])return e;var t,n=Sr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Cr)return kr[e]=n[t];return e}u&&(Cr=document.createElement("div").style,"AnimationEvent"in window||(delete Sr.animationend.animation,delete Sr.animationiteration.animation,delete Sr.animationstart.animation),"TransitionEvent"in window||delete Sr.transitionend.transition);var Tr=Er("animationend"),Dr=Er("animationiteration"),Or=Er("animationstart"),jr=Er("transitionend"),Nr=new Map,Ir="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function _r(e,t){Nr.set(e,t),s(t,[e])}for(var Pr=0;Pr<Ir.length;Pr++){var zr=Ir[Pr];_r(zr.toLowerCase(),"on"+(zr[0].toUpperCase()+zr.slice(1)))}_r(Tr,"onAnimationEnd"),_r(Dr,"onAnimationIteration"),_r(Or,"onAnimationStart"),_r("dblclick","onDoubleClick"),_r("focusin","onFocus"),_r("focusout","onBlur"),_r(jr,"onTransitionEnd"),c("onMouseEnter",["mouseout","mouseover"]),c("onMouseLeave",["mouseout","mouseover"]),c("onPointerEnter",["pointerout","pointerover"]),c("onPointerLeave",["pointerout","pointerover"]),s("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),s("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),s("onBeforeInput",["compositionend","keypress","textInput","paste"]),s("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Fr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Lr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Fr));function Rr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,i,l,s,c){if(Ue.apply(this,arguments),Le){if(!Le)throw Error(o(198));var u=Re;Le=!1,Re=null,Me||(Me=!0,Ae=u)}}(r,t,void 0,e),e.currentTarget=null}function Mr(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var i=r.length-1;0<=i;i--){var l=r[i],s=l.instance,c=l.currentTarget;if(l=l.listener,s!==o&&a.isPropagationStopped())break e;Rr(a,l,c),o=s}else for(i=0;i<r.length;i++){if(s=(l=r[i]).instance,c=l.currentTarget,l=l.listener,s!==o&&a.isPropagationStopped())break e;Rr(a,l,c),o=s}}}if(Me)throw e=Ae,Me=!1,Ae=null,e}function Ar(e,t){var n=t[ha];void 0===n&&(n=t[ha]=new Set);var r=e+"__bubble";n.has(r)||(Wr(t,e,2,!1),n.add(r))}function Hr(e,t,n){var r=0;t&&(r|=4),Wr(n,e,r,t)}var Ur="_reactListening"+Math.random().toString(36).slice(2);function Br(e){if(!e[Ur]){e[Ur]=!0,i.forEach((function(t){"selectionchange"!==t&&(Lr.has(t)||Hr(t,!1,e),Hr(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Ur]||(t[Ur]=!0,Hr("selectionchange",!1,t))}}function Wr(e,t,n,r){switch(Xt(t)){case 1:var a=$t;break;case 4:a=Yt;break;default:a=Qt}n=a.bind(null,t,n,e),a=void 0,!Pe||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Vr(e,t,n,r,a){var o=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var l=r.stateNode.containerInfo;if(l===a||8===l.nodeType&&l.parentNode===a)break;if(4===i)for(i=r.return;null!==i;){var s=i.tag;if((3===s||4===s)&&((s=i.stateNode.containerInfo)===a||8===s.nodeType&&s.parentNode===a))return;i=i.return}for(;null!==l;){if(null===(i=ya(l)))return;if(5===(s=i.tag)||6===s){r=o=i;continue e}l=l.parentNode}}r=r.return}Ie((function(){var r=o,a=we(n),i=[];e:{var l=Nr.get(e);if(void 0!==l){var s=un,c=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":s=Dn;break;case"focusin":c="focus",s=mn;break;case"focusout":c="blur",s=mn;break;case"beforeblur":case"afterblur":s=mn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=gn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=hn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=jn;break;case Tr:case Dr:case Or:s=vn;break;case jr:s=Nn;break;case"scroll":s=fn;break;case"wheel":s=_n;break;case"copy":case"cut":case"paste":s=bn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=On}var u=0!==(4&t),d=!u&&"scroll"===e,f=u?null!==l?l+"Capture":null:l;u=[];for(var p,g=r;null!==g;){var h=(p=g).stateNode;if(5===p.tag&&null!==h&&(p=h,null!==f&&(null!=(h=_e(g,f))&&u.push($r(g,h,p)))),d)break;g=g.return}0<u.length&&(l=new s(l,c,null,n,a),i.push({event:l,listeners:u}))}}if(0===(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(l="mouseover"===e||"pointerover"===e)||n===xe||!(c=n.relatedTarget||n.fromElement)||!ya(c)&&!c[ga])&&(s||l)&&(l=a.window===a?a:(l=a.ownerDocument)?l.defaultView||l.parentWindow:window,s?(s=r,null!==(c=(c=n.relatedTarget||n.toElement)?ya(c):null)&&(c!==(d=Be(c))||5!==c.tag&&6!==c.tag)&&(c=null)):(s=null,c=r),s!==c)){if(u=gn,h="onMouseLeave",f="onMouseEnter",g="mouse","pointerout"!==e&&"pointerover"!==e||(u=On,h="onPointerLeave",f="onPointerEnter",g="pointer"),d=null==s?l:xa(s),p=null==c?l:xa(c),(l=new u(h,g+"leave",s,n,a)).target=d,l.relatedTarget=p,h=null,ya(a)===r&&((u=new u(f,g+"enter",c,n,a)).target=p,u.relatedTarget=d,h=u),d=h,s&&c)e:{for(f=c,g=0,p=u=s;p;p=Qr(p))g++;for(p=0,h=f;h;h=Qr(h))p++;for(;0<g-p;)u=Qr(u),g--;for(;0<p-g;)f=Qr(f),p--;for(;g--;){if(u===f||null!==f&&u===f.alternate)break e;u=Qr(u),f=Qr(f)}u=null}else u=null;null!==s&&qr(i,l,s,u,!1),null!==c&&null!==d&&qr(i,d,c,u,!0)}if("select"===(s=(l=r?xa(r):window).nodeName&&l.nodeName.toLowerCase())||"input"===s&&"file"===l.type)var m=Xn;else if(Vn(l))if(Gn)m=ir;else{m=ar;var v=rr}else(s=l.nodeName)&&"input"===s.toLowerCase()&&("checkbox"===l.type||"radio"===l.type)&&(m=or);switch(m&&(m=m(e,r))?$n(i,m,n,a):(v&&v(e,l,r),"focusout"===e&&(v=l._wrapperState)&&v.controlled&&"number"===l.type&&ee(l,"number",l.value)),v=r?xa(r):window,e){case"focusin":(Vn(v)||"true"===v.contentEditable)&&(mr=v,vr=r,yr=null);break;case"focusout":yr=vr=mr=null;break;case"mousedown":br=!0;break;case"contextmenu":case"mouseup":case"dragend":br=!1,xr(i,n,a);break;case"selectionchange":if(hr)break;case"keydown":case"keyup":xr(i,n,a)}var y;if(zn)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Bn?Hn(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(Rn&&"ko"!==n.locale&&(Bn||"onCompositionStart"!==b?"onCompositionEnd"===b&&Bn&&(y=en()):(Zt="value"in(Gt=a)?Gt.value:Gt.textContent,Bn=!0)),0<(v=Yr(r,b)).length&&(b=new xn(b,e,null,n,a),i.push({event:b,listeners:v}),y?b.data=y:null!==(y=Un(n))&&(b.data=y))),(y=Ln?function(e,t){switch(e){case"compositionend":return Un(t);case"keypress":return 32!==t.which?null:(An=!0,Mn);case"textInput":return(e=t.data)===Mn&&An?null:e;default:return null}}(e,n):function(e,t){if(Bn)return"compositionend"===e||!zn&&Hn(e,t)?(e=en(),Jt=Zt=Gt=null,Bn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Rn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=Yr(r,"onBeforeInput")).length&&(a=new xn("onBeforeInput","beforeinput",null,n,a),i.push({event:a,listeners:r}),a.data=y))}Mr(i,t)}))}function $r(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Yr(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,o=a.stateNode;5===a.tag&&null!==o&&(a=o,null!=(o=_e(e,n))&&r.unshift($r(e,o,a)),null!=(o=_e(e,t))&&r.push($r(e,o,a))),e=e.return}return r}function Qr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function qr(e,t,n,r,a){for(var o=t._reactName,i=[];null!==n&&n!==r;){var l=n,s=l.alternate,c=l.stateNode;if(null!==s&&s===r)break;5===l.tag&&null!==c&&(l=c,a?null!=(s=_e(n,o))&&i.unshift($r(n,s,l)):a||null!=(s=_e(n,o))&&i.push($r(n,s,l))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}var Kr=/\r\n?/g,Xr=/\u0000|\uFFFD/g;function Gr(e){return("string"===typeof e?e:""+e).replace(Kr,"\n").replace(Xr,"")}function Zr(e,t,n){if(t=Gr(t),Gr(e)!==t&&n)throw Error(o(425))}function Jr(){}var ea=null,ta=null;function na(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ra="function"===typeof setTimeout?setTimeout:void 0,aa="function"===typeof clearTimeout?clearTimeout:void 0,oa="function"===typeof Promise?Promise:void 0,ia="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof oa?function(e){return oa.resolve(null).then(e).catch(la)}:ra;function la(e){setTimeout((function(){throw e}))}function sa(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void Bt(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);Bt(t)}function ca(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ua(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var da=Math.random().toString(36).slice(2),fa="__reactFiber$"+da,pa="__reactProps$"+da,ga="__reactContainer$"+da,ha="__reactEvents$"+da,ma="__reactListeners$"+da,va="__reactHandles$"+da;function ya(e){var t=e[fa];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ga]||n[fa]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ua(e);null!==e;){if(n=e[fa])return n;e=ua(e)}return t}n=(e=n).parentNode}return null}function ba(e){return!(e=e[fa]||e[ga])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function xa(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(o(33))}function wa(e){return e[pa]||null}var Sa=[],ka=-1;function Ca(e){return{current:e}}function Ea(e){0>ka||(e.current=Sa[ka],Sa[ka]=null,ka--)}function Ta(e,t){ka++,Sa[ka]=e.current,e.current=t}var Da={},Oa=Ca(Da),ja=Ca(!1),Na=Da;function Ia(e,t){var n=e.type.contextTypes;if(!n)return Da;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,o={};for(a in n)o[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function _a(e){return null!==(e=e.childContextTypes)&&void 0!==e}function Pa(){Ea(ja),Ea(Oa)}function za(e,t,n){if(Oa.current!==Da)throw Error(o(168));Ta(Oa,t),Ta(ja,n)}function Fa(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(o(108,W(e)||"Unknown",a));return R({},n,r)}function La(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Da,Na=Oa.current,Ta(Oa,e),Ta(ja,ja.current),!0}function Ra(e,t,n){var r=e.stateNode;if(!r)throw Error(o(169));n?(e=Fa(e,t,Na),r.__reactInternalMemoizedMergedChildContext=e,Ea(ja),Ea(Oa),Ta(Oa,e)):Ea(ja),Ta(ja,n)}var Ma=null,Aa=!1,Ha=!1;function Ua(e){null===Ma?Ma=[e]:Ma.push(e)}function Ba(){if(!Ha&&null!==Ma){Ha=!0;var e=0,t=bt;try{var n=Ma;for(bt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Ma=null,Aa=!1}catch(a){throw null!==Ma&&(Ma=Ma.slice(e+1)),Qe(Je,Ba),a}finally{bt=t,Ha=!1}}return null}var Wa=[],Va=0,$a=null,Ya=0,Qa=[],qa=0,Ka=null,Xa=1,Ga="";function Za(e,t){Wa[Va++]=Ya,Wa[Va++]=$a,$a=e,Ya=t}function Ja(e,t,n){Qa[qa++]=Xa,Qa[qa++]=Ga,Qa[qa++]=Ka,Ka=e;var r=Xa;e=Ga;var a=32-it(r)-1;r&=~(1<<a),n+=1;var o=32-it(t)+a;if(30<o){var i=a-a%5;o=(r&(1<<i)-1).toString(32),r>>=i,a-=i,Xa=1<<32-it(t)+a|n<<a|r,Ga=o+e}else Xa=1<<o|n<<a|r,Ga=e}function eo(e){null!==e.return&&(Za(e,1),Ja(e,1,0))}function to(e){for(;e===$a;)$a=Wa[--Va],Wa[Va]=null,Ya=Wa[--Va],Wa[Va]=null;for(;e===Ka;)Ka=Qa[--qa],Qa[qa]=null,Ga=Qa[--qa],Qa[qa]=null,Xa=Qa[--qa],Qa[qa]=null}var no=null,ro=null,ao=!1,oo=null;function io(e,t){var n=Ic(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function lo(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,no=e,ro=ca(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,no=e,ro=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Ka?{id:Xa,overflow:Ga}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Ic(18,null,null,0)).stateNode=t,n.return=e,e.child=n,no=e,ro=null,!0);default:return!1}}function so(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function co(e){if(ao){var t=ro;if(t){var n=t;if(!lo(e,t)){if(so(e))throw Error(o(418));t=ca(n.nextSibling);var r=no;t&&lo(e,t)?io(r,n):(e.flags=-4097&e.flags|2,ao=!1,no=e)}}else{if(so(e))throw Error(o(418));e.flags=-4097&e.flags|2,ao=!1,no=e}}}function uo(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;no=e}function fo(e){if(e!==no)return!1;if(!ao)return uo(e),ao=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!na(e.type,e.memoizedProps)),t&&(t=ro)){if(so(e))throw po(),Error(o(418));for(;t;)io(e,t),t=ca(t.nextSibling)}if(uo(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){ro=ca(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}ro=null}}else ro=no?ca(e.stateNode.nextSibling):null;return!0}function po(){for(var e=ro;e;)e=ca(e.nextSibling)}function go(){ro=no=null,ao=!1}function ho(e){null===oo?oo=[e]:oo.push(e)}var mo=x.ReactCurrentBatchConfig;function vo(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(o(309));var r=n.stateNode}if(!r)throw Error(o(147,e));var a=r,i=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===i?t.ref:(t=function(e){var t=a.refs;null===e?delete t[i]:t[i]=e},t._stringRef=i,t)}if("string"!==typeof e)throw Error(o(284));if(!n._owner)throw Error(o(290,e))}return e}function yo(e,t){throw e=Object.prototype.toString.call(t),Error(o(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function bo(e){return(0,e._init)(e._payload)}function xo(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=Pc(e,t)).index=0,e.sibling=null,e}function i(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function l(t){return e&&null===t.alternate&&(t.flags|=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Rc(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function c(e,t,n,r){var o=n.type;return o===k?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===o||"object"===typeof o&&null!==o&&o.$$typeof===_&&bo(o)===t.type)?((r=a(t,n.props)).ref=vo(e,t,n),r.return=e,r):((r=zc(n.type,n.key,n.props,null,e.mode,r)).ref=vo(e,t,n),r.return=e,r)}function u(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Mc(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,o){return null===t||7!==t.tag?((t=Fc(n,e.mode,r,o)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Rc(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case w:return(n=zc(t.type,t.key,t.props,null,e.mode,n)).ref=vo(e,null,t),n.return=e,n;case S:return(t=Mc(t,e.mode,n)).return=e,t;case _:return f(e,(0,t._init)(t._payload),n)}if(te(t)||F(t))return(t=Fc(t,e.mode,n,null)).return=e,t;yo(e,t)}return null}function p(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==a?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case w:return n.key===a?c(e,t,n,r):null;case S:return n.key===a?u(e,t,n,r):null;case _:return p(e,t,(a=n._init)(n._payload),r)}if(te(n)||F(n))return null!==a?null:d(e,t,n,r,null);yo(e,n)}return null}function g(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r)return s(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case w:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case S:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case _:return g(e,t,n,(0,r._init)(r._payload),a)}if(te(r)||F(r))return d(t,e=e.get(n)||null,r,a,null);yo(t,r)}return null}function h(a,o,l,s){for(var c=null,u=null,d=o,h=o=0,m=null;null!==d&&h<l.length;h++){d.index>h?(m=d,d=null):m=d.sibling;var v=p(a,d,l[h],s);if(null===v){null===d&&(d=m);break}e&&d&&null===v.alternate&&t(a,d),o=i(v,o,h),null===u?c=v:u.sibling=v,u=v,d=m}if(h===l.length)return n(a,d),ao&&Za(a,h),c;if(null===d){for(;h<l.length;h++)null!==(d=f(a,l[h],s))&&(o=i(d,o,h),null===u?c=d:u.sibling=d,u=d);return ao&&Za(a,h),c}for(d=r(a,d);h<l.length;h++)null!==(m=g(d,a,h,l[h],s))&&(e&&null!==m.alternate&&d.delete(null===m.key?h:m.key),o=i(m,o,h),null===u?c=m:u.sibling=m,u=m);return e&&d.forEach((function(e){return t(a,e)})),ao&&Za(a,h),c}function m(a,l,s,c){var u=F(s);if("function"!==typeof u)throw Error(o(150));if(null==(s=u.call(s)))throw Error(o(151));for(var d=u=null,h=l,m=l=0,v=null,y=s.next();null!==h&&!y.done;m++,y=s.next()){h.index>m?(v=h,h=null):v=h.sibling;var b=p(a,h,y.value,c);if(null===b){null===h&&(h=v);break}e&&h&&null===b.alternate&&t(a,h),l=i(b,l,m),null===d?u=b:d.sibling=b,d=b,h=v}if(y.done)return n(a,h),ao&&Za(a,m),u;if(null===h){for(;!y.done;m++,y=s.next())null!==(y=f(a,y.value,c))&&(l=i(y,l,m),null===d?u=y:d.sibling=y,d=y);return ao&&Za(a,m),u}for(h=r(a,h);!y.done;m++,y=s.next())null!==(y=g(h,a,m,y.value,c))&&(e&&null!==y.alternate&&h.delete(null===y.key?m:y.key),l=i(y,l,m),null===d?u=y:d.sibling=y,d=y);return e&&h.forEach((function(e){return t(a,e)})),ao&&Za(a,m),u}return function e(r,o,i,s){if("object"===typeof i&&null!==i&&i.type===k&&null===i.key&&(i=i.props.children),"object"===typeof i&&null!==i){switch(i.$$typeof){case w:e:{for(var c=i.key,u=o;null!==u;){if(u.key===c){if((c=i.type)===k){if(7===u.tag){n(r,u.sibling),(o=a(u,i.props.children)).return=r,r=o;break e}}else if(u.elementType===c||"object"===typeof c&&null!==c&&c.$$typeof===_&&bo(c)===u.type){n(r,u.sibling),(o=a(u,i.props)).ref=vo(r,u,i),o.return=r,r=o;break e}n(r,u);break}t(r,u),u=u.sibling}i.type===k?((o=Fc(i.props.children,r.mode,s,i.key)).return=r,r=o):((s=zc(i.type,i.key,i.props,null,r.mode,s)).ref=vo(r,o,i),s.return=r,r=s)}return l(r);case S:e:{for(u=i.key;null!==o;){if(o.key===u){if(4===o.tag&&o.stateNode.containerInfo===i.containerInfo&&o.stateNode.implementation===i.implementation){n(r,o.sibling),(o=a(o,i.children||[])).return=r,r=o;break e}n(r,o);break}t(r,o),o=o.sibling}(o=Mc(i,r.mode,s)).return=r,r=o}return l(r);case _:return e(r,o,(u=i._init)(i._payload),s)}if(te(i))return h(r,o,i,s);if(F(i))return m(r,o,i,s);yo(r,i)}return"string"===typeof i&&""!==i||"number"===typeof i?(i=""+i,null!==o&&6===o.tag?(n(r,o.sibling),(o=a(o,i)).return=r,r=o):(n(r,o),(o=Rc(i,r.mode,s)).return=r,r=o),l(r)):n(r,o)}}var wo=xo(!0),So=xo(!1),ko=Ca(null),Co=null,Eo=null,To=null;function Do(){To=Eo=Co=null}function Oo(e){var t=ko.current;Ea(ko),e._currentValue=t}function jo(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function No(e,t){Co=e,To=Eo=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(bl=!0),e.firstContext=null)}function Io(e){var t=e._currentValue;if(To!==e)if(e={context:e,memoizedValue:t,next:null},null===Eo){if(null===Co)throw Error(o(308));Eo=e,Co.dependencies={lanes:0,firstContext:e}}else Eo=Eo.next=e;return t}var _o=null;function Po(e){null===_o?_o=[e]:_o.push(e)}function zo(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,Po(t)):(n.next=a.next,a.next=n),t.interleaved=n,Fo(e,r)}function Fo(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Lo=!1;function Ro(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Mo(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ao(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ho(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&Os)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,Fo(e,n)}return null===(a=r.interleaved)?(t.next=t,Po(r)):(t.next=a.next,a.next=t),r.interleaved=t,Fo(e,n)}function Uo(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}function Bo(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,o=null;if(null!==(n=n.firstBaseUpdate)){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===o?a=o=i:o=o.next=i,n=n.next}while(null!==n);null===o?a=o=t:o=o.next=t}else a=o=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:o,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Wo(e,t,n,r){var a=e.updateQueue;Lo=!1;var o=a.firstBaseUpdate,i=a.lastBaseUpdate,l=a.shared.pending;if(null!==l){a.shared.pending=null;var s=l,c=s.next;s.next=null,null===i?o=c:i.next=c,i=s;var u=e.alternate;null!==u&&((l=(u=u.updateQueue).lastBaseUpdate)!==i&&(null===l?u.firstBaseUpdate=c:l.next=c,u.lastBaseUpdate=s))}if(null!==o){var d=a.baseState;for(i=0,u=c=s=null,l=o;;){var f=l.lane,p=l.eventTime;if((r&f)===f){null!==u&&(u=u.next={eventTime:p,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var g=e,h=l;switch(f=t,p=n,h.tag){case 1:if("function"===typeof(g=h.payload)){d=g.call(p,d,f);break e}d=g;break e;case 3:g.flags=-65537&g.flags|128;case 0:if(null===(f="function"===typeof(g=h.payload)?g.call(p,d,f):g)||void 0===f)break e;d=R({},d,f);break e;case 2:Lo=!0}}null!==l.callback&&0!==l.lane&&(e.flags|=64,null===(f=a.effects)?a.effects=[l]:f.push(l))}else p={eventTime:p,lane:f,tag:l.tag,payload:l.payload,callback:l.callback,next:null},null===u?(c=u=p,s=d):u=u.next=p,i|=f;if(null===(l=l.next)){if(null===(l=a.shared.pending))break;l=(f=l).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}if(null===u&&(s=d),a.baseState=s,a.firstBaseUpdate=c,a.lastBaseUpdate=u,null!==(t=a.shared.interleaved)){a=t;do{i|=a.lane,a=a.next}while(a!==t)}else null===o&&(a.shared.lanes=0);Ls|=i,e.lanes=i,e.memoizedState=d}}function Vo(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!==typeof a)throw Error(o(191,a));a.call(r)}}}var $o={},Yo=Ca($o),Qo=Ca($o),qo=Ca($o);function Ko(e){if(e===$o)throw Error(o(174));return e}function Xo(e,t){switch(Ta(qo,t),Ta(Qo,e),Ta(Yo,$o),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:se(null,"");break;default:t=se(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Ea(Yo),Ta(Yo,t)}function Go(){Ea(Yo),Ea(Qo),Ea(qo)}function Zo(e){Ko(qo.current);var t=Ko(Yo.current),n=se(t,e.type);t!==n&&(Ta(Qo,e),Ta(Yo,n))}function Jo(e){Qo.current===e&&(Ea(Yo),Ea(Qo))}var ei=Ca(0);function ti(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ni=[];function ri(){for(var e=0;e<ni.length;e++)ni[e]._workInProgressVersionPrimary=null;ni.length=0}var ai=x.ReactCurrentDispatcher,oi=x.ReactCurrentBatchConfig,ii=0,li=null,si=null,ci=null,ui=!1,di=!1,fi=0,pi=0;function gi(){throw Error(o(321))}function hi(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!lr(e[n],t[n]))return!1;return!0}function mi(e,t,n,r,a,i){if(ii=i,li=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ai.current=null===e||null===e.memoizedState?Ji:el,e=n(r,a),di){i=0;do{if(di=!1,fi=0,25<=i)throw Error(o(301));i+=1,ci=si=null,t.updateQueue=null,ai.current=tl,e=n(r,a)}while(di)}if(ai.current=Zi,t=null!==si&&null!==si.next,ii=0,ci=si=li=null,ui=!1,t)throw Error(o(300));return e}function vi(){var e=0!==fi;return fi=0,e}function yi(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===ci?li.memoizedState=ci=e:ci=ci.next=e,ci}function bi(){if(null===si){var e=li.alternate;e=null!==e?e.memoizedState:null}else e=si.next;var t=null===ci?li.memoizedState:ci.next;if(null!==t)ci=t,si=e;else{if(null===e)throw Error(o(310));e={memoizedState:(si=e).memoizedState,baseState:si.baseState,baseQueue:si.baseQueue,queue:si.queue,next:null},null===ci?li.memoizedState=ci=e:ci=ci.next=e}return ci}function xi(e,t){return"function"===typeof t?t(e):t}function wi(e){var t=bi(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=si,a=r.baseQueue,i=n.pending;if(null!==i){if(null!==a){var l=a.next;a.next=i.next,i.next=l}r.baseQueue=a=i,n.pending=null}if(null!==a){i=a.next,r=r.baseState;var s=l=null,c=null,u=i;do{var d=u.lane;if((ii&d)===d)null!==c&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};null===c?(s=c=f,l=r):c=c.next=f,li.lanes|=d,Ls|=d}u=u.next}while(null!==u&&u!==i);null===c?l=r:c.next=s,lr(r,t.memoizedState)||(bl=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=c,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{i=a.lane,li.lanes|=i,Ls|=i,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Si(e){var t=bi(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,i=t.memoizedState;if(null!==a){n.pending=null;var l=a=a.next;do{i=e(i,l.action),l=l.next}while(l!==a);lr(i,t.memoizedState)||(bl=!0),t.memoizedState=i,null===t.baseQueue&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function ki(){}function Ci(e,t){var n=li,r=bi(),a=t(),i=!lr(r.memoizedState,a);if(i&&(r.memoizedState=a,bl=!0),r=r.queue,Li(Di.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||null!==ci&&1&ci.memoizedState.tag){if(n.flags|=2048,Ii(9,Ti.bind(null,n,r,a,t),void 0,null),null===js)throw Error(o(349));0!==(30&ii)||Ei(n,t,a)}return a}function Ei(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=li.updateQueue)?(t={lastEffect:null,stores:null},li.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Ti(e,t,n,r){t.value=n,t.getSnapshot=r,Oi(t)&&ji(e)}function Di(e,t,n){return n((function(){Oi(t)&&ji(e)}))}function Oi(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!lr(e,n)}catch(r){return!0}}function ji(e){var t=Fo(e,1);null!==t&&nc(t,e,1,-1)}function Ni(e){var t=yi();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:xi,lastRenderedState:e},t.queue=e,e=e.dispatch=qi.bind(null,li,e),[t.memoizedState,e]}function Ii(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=li.updateQueue)?(t={lastEffect:null,stores:null},li.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function _i(){return bi().memoizedState}function Pi(e,t,n,r){var a=yi();li.flags|=e,a.memoizedState=Ii(1|t,n,void 0,void 0===r?null:r)}function zi(e,t,n,r){var a=bi();r=void 0===r?null:r;var o=void 0;if(null!==si){var i=si.memoizedState;if(o=i.destroy,null!==r&&hi(r,i.deps))return void(a.memoizedState=Ii(t,n,o,r))}li.flags|=e,a.memoizedState=Ii(1|t,n,o,r)}function Fi(e,t){return Pi(8390656,8,e,t)}function Li(e,t){return zi(2048,8,e,t)}function Ri(e,t){return zi(4,2,e,t)}function Mi(e,t){return zi(4,4,e,t)}function Ai(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Hi(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,zi(4,4,Ai.bind(null,t,e),n)}function Ui(){}function Bi(e,t){var n=bi();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&hi(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Wi(e,t){var n=bi();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&hi(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Vi(e,t,n){return 0===(21&ii)?(e.baseState&&(e.baseState=!1,bl=!0),e.memoizedState=n):(lr(n,t)||(n=ht(),li.lanes|=n,Ls|=n,e.baseState=!0),t)}function $i(e,t){var n=bt;bt=0!==n&&4>n?n:4,e(!0);var r=oi.transition;oi.transition={};try{e(!1),t()}finally{bt=n,oi.transition=r}}function Yi(){return bi().memoizedState}function Qi(e,t,n){var r=tc(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Ki(e))Xi(t,n);else if(null!==(n=zo(e,t,n,r))){nc(n,e,r,ec()),Gi(n,t,r)}}function qi(e,t,n){var r=tc(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ki(e))Xi(t,a);else{var o=e.alternate;if(0===e.lanes&&(null===o||0===o.lanes)&&null!==(o=t.lastRenderedReducer))try{var i=t.lastRenderedState,l=o(i,n);if(a.hasEagerState=!0,a.eagerState=l,lr(l,i)){var s=t.interleaved;return null===s?(a.next=a,Po(t)):(a.next=s.next,s.next=a),void(t.interleaved=a)}}catch(c){}null!==(n=zo(e,t,a,r))&&(nc(n,e,r,a=ec()),Gi(n,t,r))}}function Ki(e){var t=e.alternate;return e===li||null!==t&&t===li}function Xi(e,t){di=ui=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Gi(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}var Zi={readContext:Io,useCallback:gi,useContext:gi,useEffect:gi,useImperativeHandle:gi,useInsertionEffect:gi,useLayoutEffect:gi,useMemo:gi,useReducer:gi,useRef:gi,useState:gi,useDebugValue:gi,useDeferredValue:gi,useTransition:gi,useMutableSource:gi,useSyncExternalStore:gi,useId:gi,unstable_isNewReconciler:!1},Ji={readContext:Io,useCallback:function(e,t){return yi().memoizedState=[e,void 0===t?null:t],e},useContext:Io,useEffect:Fi,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Pi(4194308,4,Ai.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Pi(4194308,4,e,t)},useInsertionEffect:function(e,t){return Pi(4,2,e,t)},useMemo:function(e,t){var n=yi();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=yi();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Qi.bind(null,li,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},yi().memoizedState=e},useState:Ni,useDebugValue:Ui,useDeferredValue:function(e){return yi().memoizedState=e},useTransition:function(){var e=Ni(!1),t=e[0];return e=$i.bind(null,e[1]),yi().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=li,a=yi();if(ao){if(void 0===n)throw Error(o(407));n=n()}else{if(n=t(),null===js)throw Error(o(349));0!==(30&ii)||Ei(r,t,n)}a.memoizedState=n;var i={value:n,getSnapshot:t};return a.queue=i,Fi(Di.bind(null,r,i,e),[e]),r.flags|=2048,Ii(9,Ti.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=yi(),t=js.identifierPrefix;if(ao){var n=Ga;t=":"+t+"R"+(n=(Xa&~(1<<32-it(Xa)-1)).toString(32)+n),0<(n=fi++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=pi++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},el={readContext:Io,useCallback:Bi,useContext:Io,useEffect:Li,useImperativeHandle:Hi,useInsertionEffect:Ri,useLayoutEffect:Mi,useMemo:Wi,useReducer:wi,useRef:_i,useState:function(){return wi(xi)},useDebugValue:Ui,useDeferredValue:function(e){return Vi(bi(),si.memoizedState,e)},useTransition:function(){return[wi(xi)[0],bi().memoizedState]},useMutableSource:ki,useSyncExternalStore:Ci,useId:Yi,unstable_isNewReconciler:!1},tl={readContext:Io,useCallback:Bi,useContext:Io,useEffect:Li,useImperativeHandle:Hi,useInsertionEffect:Ri,useLayoutEffect:Mi,useMemo:Wi,useReducer:Si,useRef:_i,useState:function(){return Si(xi)},useDebugValue:Ui,useDeferredValue:function(e){var t=bi();return null===si?t.memoizedState=e:Vi(t,si.memoizedState,e)},useTransition:function(){return[Si(xi)[0],bi().memoizedState]},useMutableSource:ki,useSyncExternalStore:Ci,useId:Yi,unstable_isNewReconciler:!1};function nl(e,t){if(e&&e.defaultProps){for(var n in t=R({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function rl(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:R({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var al={isMounted:function(e){return!!(e=e._reactInternals)&&Be(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ec(),a=tc(e),o=Ao(r,a);o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=Ho(e,o,a))&&(nc(t,e,a,r),Uo(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ec(),a=tc(e),o=Ao(r,a);o.tag=1,o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=Ho(e,o,a))&&(nc(t,e,a,r),Uo(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ec(),r=tc(e),a=Ao(n,r);a.tag=2,void 0!==t&&null!==t&&(a.callback=t),null!==(t=Ho(e,a,r))&&(nc(t,e,r,n),Uo(t,e,r))}};function ol(e,t,n,r,a,o,i){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,o,i):!t.prototype||!t.prototype.isPureReactComponent||(!sr(n,r)||!sr(a,o))}function il(e,t,n){var r=!1,a=Da,o=t.contextType;return"object"===typeof o&&null!==o?o=Io(o):(a=_a(t)?Na:Oa.current,o=(r=null!==(r=t.contextTypes)&&void 0!==r)?Ia(e,a):Da),t=new t(n,o),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=al,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=o),t}function ll(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&al.enqueueReplaceState(t,t.state,null)}function sl(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Ro(e);var o=t.contextType;"object"===typeof o&&null!==o?a.context=Io(o):(o=_a(t)?Na:Oa.current,a.context=Ia(e,o)),a.state=e.memoizedState,"function"===typeof(o=t.getDerivedStateFromProps)&&(rl(e,t,o,n),a.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(t=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&al.enqueueReplaceState(a,a.state,null),Wo(e,n,a,r),a.state=e.memoizedState),"function"===typeof a.componentDidMount&&(e.flags|=4194308)}function cl(e,t){try{var n="",r=t;do{n+=U(r),r=r.return}while(r);var a=n}catch(o){a="\nError generating stack: "+o.message+"\n"+o.stack}return{value:e,source:t,stack:a,digest:null}}function ul(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function dl(e,t){try{console.error(t.value)}catch(n){setTimeout((function(){throw n}))}}var fl="function"===typeof WeakMap?WeakMap:Map;function pl(e,t,n){(n=Ao(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Vs||(Vs=!0,$s=r),dl(0,t)},n}function gl(e,t,n){(n=Ao(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){dl(0,t)}}var o=e.stateNode;return null!==o&&"function"===typeof o.componentDidCatch&&(n.callback=function(){dl(0,t),"function"!==typeof r&&(null===Ys?Ys=new Set([this]):Ys.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function hl(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new fl;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=Ec.bind(null,e,t,n),t.then(e,e))}function ml(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function vl(e,t,n,r,a){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Ao(-1,1)).tag=2,Ho(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}var yl=x.ReactCurrentOwner,bl=!1;function xl(e,t,n,r){t.child=null===e?So(t,null,n,r):wo(t,e.child,n,r)}function wl(e,t,n,r,a){n=n.render;var o=t.ref;return No(t,a),r=mi(e,t,n,r,o,a),n=vi(),null===e||bl?(ao&&n&&eo(t),t.flags|=1,xl(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Vl(e,t,a))}function Sl(e,t,n,r,a){if(null===e){var o=n.type;return"function"!==typeof o||_c(o)||void 0!==o.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=zc(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=o,kl(e,t,o,r,a))}if(o=e.child,0===(e.lanes&a)){var i=o.memoizedProps;if((n=null!==(n=n.compare)?n:sr)(i,r)&&e.ref===t.ref)return Vl(e,t,a)}return t.flags|=1,(e=Pc(o,r)).ref=t.ref,e.return=t,t.child=e}function kl(e,t,n,r,a){if(null!==e){var o=e.memoizedProps;if(sr(o,r)&&e.ref===t.ref){if(bl=!1,t.pendingProps=r=o,0===(e.lanes&a))return t.lanes=e.lanes,Vl(e,t,a);0!==(131072&e.flags)&&(bl=!0)}}return Tl(e,t,n,r,a)}function Cl(e,t,n){var r=t.pendingProps,a=r.children,o=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ta(Ps,_s),_s|=n;else{if(0===(1073741824&n))return e=null!==o?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ta(Ps,_s),_s|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==o?o.baseLanes:n,Ta(Ps,_s),_s|=r}else null!==o?(r=o.baseLanes|n,t.memoizedState=null):r=n,Ta(Ps,_s),_s|=r;return xl(e,t,a,n),t.child}function El(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Tl(e,t,n,r,a){var o=_a(n)?Na:Oa.current;return o=Ia(t,o),No(t,a),n=mi(e,t,n,r,o,a),r=vi(),null===e||bl?(ao&&r&&eo(t),t.flags|=1,xl(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Vl(e,t,a))}function Dl(e,t,n,r,a){if(_a(n)){var o=!0;La(t)}else o=!1;if(No(t,a),null===t.stateNode)Wl(e,t),il(t,n,r),sl(t,n,r,a),r=!0;else if(null===e){var i=t.stateNode,l=t.memoizedProps;i.props=l;var s=i.context,c=n.contextType;"object"===typeof c&&null!==c?c=Io(c):c=Ia(t,c=_a(n)?Na:Oa.current);var u=n.getDerivedStateFromProps,d="function"===typeof u||"function"===typeof i.getSnapshotBeforeUpdate;d||"function"!==typeof i.UNSAFE_componentWillReceiveProps&&"function"!==typeof i.componentWillReceiveProps||(l!==r||s!==c)&&ll(t,i,r,c),Lo=!1;var f=t.memoizedState;i.state=f,Wo(t,r,i,a),s=t.memoizedState,l!==r||f!==s||ja.current||Lo?("function"===typeof u&&(rl(t,n,u,r),s=t.memoizedState),(l=Lo||ol(t,n,l,r,f,s,c))?(d||"function"!==typeof i.UNSAFE_componentWillMount&&"function"!==typeof i.componentWillMount||("function"===typeof i.componentWillMount&&i.componentWillMount(),"function"===typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount()),"function"===typeof i.componentDidMount&&(t.flags|=4194308)):("function"===typeof i.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),i.props=r,i.state=s,i.context=c,r=l):("function"===typeof i.componentDidMount&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,Mo(e,t),l=t.memoizedProps,c=t.type===t.elementType?l:nl(t.type,l),i.props=c,d=t.pendingProps,f=i.context,"object"===typeof(s=n.contextType)&&null!==s?s=Io(s):s=Ia(t,s=_a(n)?Na:Oa.current);var p=n.getDerivedStateFromProps;(u="function"===typeof p||"function"===typeof i.getSnapshotBeforeUpdate)||"function"!==typeof i.UNSAFE_componentWillReceiveProps&&"function"!==typeof i.componentWillReceiveProps||(l!==d||f!==s)&&ll(t,i,r,s),Lo=!1,f=t.memoizedState,i.state=f,Wo(t,r,i,a);var g=t.memoizedState;l!==d||f!==g||ja.current||Lo?("function"===typeof p&&(rl(t,n,p,r),g=t.memoizedState),(c=Lo||ol(t,n,c,r,f,g,s)||!1)?(u||"function"!==typeof i.UNSAFE_componentWillUpdate&&"function"!==typeof i.componentWillUpdate||("function"===typeof i.componentWillUpdate&&i.componentWillUpdate(r,g,s),"function"===typeof i.UNSAFE_componentWillUpdate&&i.UNSAFE_componentWillUpdate(r,g,s)),"function"===typeof i.componentDidUpdate&&(t.flags|=4),"function"===typeof i.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof i.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof i.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=g),i.props=r,i.state=g,i.context=s,r=c):("function"!==typeof i.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof i.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Ol(e,t,n,r,o,a)}function Ol(e,t,n,r,a,o){El(e,t);var i=0!==(128&t.flags);if(!r&&!i)return a&&Ra(t,n,!1),Vl(e,t,o);r=t.stateNode,yl.current=t;var l=i&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&i?(t.child=wo(t,e.child,null,o),t.child=wo(t,null,l,o)):xl(e,t,l,o),t.memoizedState=r.state,a&&Ra(t,n,!0),t.child}function jl(e){var t=e.stateNode;t.pendingContext?za(0,t.pendingContext,t.pendingContext!==t.context):t.context&&za(0,t.context,!1),Xo(e,t.containerInfo)}function Nl(e,t,n,r,a){return go(),ho(a),t.flags|=256,xl(e,t,n,r),t.child}var Il,_l,Pl,zl,Fl={dehydrated:null,treeContext:null,retryLane:0};function Ll(e){return{baseLanes:e,cachePool:null,transitions:null}}function Rl(e,t,n){var r,a=t.pendingProps,i=ei.current,l=!1,s=0!==(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&0!==(2&i)),r?(l=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(i|=1),Ta(ei,1&i),null===e)return co(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(s=a.children,e=a.fallback,l?(a=t.mode,l=t.child,s={mode:"hidden",children:s},0===(1&a)&&null!==l?(l.childLanes=0,l.pendingProps=s):l=Lc(s,a,0,null),e=Fc(e,a,n,null),l.return=t,e.return=t,l.sibling=e,t.child=l,t.child.memoizedState=Ll(n),t.memoizedState=Fl,e):Ml(t,s));if(null!==(i=e.memoizedState)&&null!==(r=i.dehydrated))return function(e,t,n,r,a,i,l){if(n)return 256&t.flags?(t.flags&=-257,Al(e,t,l,r=ul(Error(o(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(i=r.fallback,a=t.mode,r=Lc({mode:"visible",children:r.children},a,0,null),(i=Fc(i,a,l,null)).flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,0!==(1&t.mode)&&wo(t,e.child,null,l),t.child.memoizedState=Ll(l),t.memoizedState=Fl,i);if(0===(1&t.mode))return Al(e,t,l,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var s=r.dgst;return r=s,Al(e,t,l,r=ul(i=Error(o(419)),r,void 0))}if(s=0!==(l&e.childLanes),bl||s){if(null!==(r=js)){switch(l&-l){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=0!==(a&(r.suspendedLanes|l))?0:a)&&a!==i.retryLane&&(i.retryLane=a,Fo(e,a),nc(r,e,a,-1))}return hc(),Al(e,t,l,r=ul(Error(o(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=Dc.bind(null,e),a._reactRetry=t,null):(e=i.treeContext,ro=ca(a.nextSibling),no=t,ao=!0,oo=null,null!==e&&(Qa[qa++]=Xa,Qa[qa++]=Ga,Qa[qa++]=Ka,Xa=e.id,Ga=e.overflow,Ka=t),t=Ml(t,r.children),t.flags|=4096,t)}(e,t,s,a,r,i,n);if(l){l=a.fallback,s=t.mode,r=(i=e.child).sibling;var c={mode:"hidden",children:a.children};return 0===(1&s)&&t.child!==i?((a=t.child).childLanes=0,a.pendingProps=c,t.deletions=null):(a=Pc(i,c)).subtreeFlags=14680064&i.subtreeFlags,null!==r?l=Pc(r,l):(l=Fc(l,s,n,null)).flags|=2,l.return=t,a.return=t,a.sibling=l,t.child=a,a=l,l=t.child,s=null===(s=e.child.memoizedState)?Ll(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},l.memoizedState=s,l.childLanes=e.childLanes&~n,t.memoizedState=Fl,a}return e=(l=e.child).sibling,a=Pc(l,{mode:"visible",children:a.children}),0===(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function Ml(e,t){return(t=Lc({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Al(e,t,n,r){return null!==r&&ho(r),wo(t,e.child,null,n),(e=Ml(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Hl(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),jo(e.return,t,n)}function Ul(e,t,n,r,a){var o=e.memoizedState;null===o?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=a)}function Bl(e,t,n){var r=t.pendingProps,a=r.revealOrder,o=r.tail;if(xl(e,t,r.children,n),0!==(2&(r=ei.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Hl(e,n,t);else if(19===e.tag)Hl(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Ta(ei,r),0===(1&t.mode))t.memoizedState=null;else switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===ti(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Ul(t,!1,a,n,o);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===ti(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Ul(t,!0,n,null,o);break;case"together":Ul(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Wl(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Vl(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Ls|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(o(153));if(null!==t.child){for(n=Pc(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Pc(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function $l(e,t){if(!ao)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Yl(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Ql(e,t,n){var r=t.pendingProps;switch(to(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Yl(t),null;case 1:case 17:return _a(t.type)&&Pa(),Yl(t),null;case 3:return r=t.stateNode,Go(),Ea(ja),Ea(Oa),ri(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(fo(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==oo&&(ic(oo),oo=null))),_l(e,t),Yl(t),null;case 5:Jo(t);var a=Ko(qo.current);if(n=t.type,null!==e&&null!=t.stateNode)Pl(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(o(166));return Yl(t),null}if(e=Ko(Yo.current),fo(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[fa]=t,r[pa]=i,e=0!==(1&t.mode),n){case"dialog":Ar("cancel",r),Ar("close",r);break;case"iframe":case"object":case"embed":Ar("load",r);break;case"video":case"audio":for(a=0;a<Fr.length;a++)Ar(Fr[a],r);break;case"source":Ar("error",r);break;case"img":case"image":case"link":Ar("error",r),Ar("load",r);break;case"details":Ar("toggle",r);break;case"input":X(r,i),Ar("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},Ar("invalid",r);break;case"textarea":ae(r,i),Ar("invalid",r)}for(var s in ye(n,i),a=null,i)if(i.hasOwnProperty(s)){var c=i[s];"children"===s?"string"===typeof c?r.textContent!==c&&(!0!==i.suppressHydrationWarning&&Zr(r.textContent,c,e),a=["children",c]):"number"===typeof c&&r.textContent!==""+c&&(!0!==i.suppressHydrationWarning&&Zr(r.textContent,c,e),a=["children",""+c]):l.hasOwnProperty(s)&&null!=c&&"onScroll"===s&&Ar("scroll",r)}switch(n){case"input":Y(r),J(r,i,!0);break;case"textarea":Y(r),ie(r);break;case"select":case"option":break;default:"function"===typeof i.onClick&&(r.onclick=Jr)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{s=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=le(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),"select"===n&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[fa]=t,e[pa]=r,Il(e,t,!1,!1),t.stateNode=e;e:{switch(s=be(n,r),n){case"dialog":Ar("cancel",e),Ar("close",e),a=r;break;case"iframe":case"object":case"embed":Ar("load",e),a=r;break;case"video":case"audio":for(a=0;a<Fr.length;a++)Ar(Fr[a],e);a=r;break;case"source":Ar("error",e),a=r;break;case"img":case"image":case"link":Ar("error",e),Ar("load",e),a=r;break;case"details":Ar("toggle",e),a=r;break;case"input":X(e,r),a=K(e,r),Ar("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=R({},r,{value:void 0}),Ar("invalid",e);break;case"textarea":ae(e,r),a=re(e,r),Ar("invalid",e)}for(i in ye(n,a),c=a)if(c.hasOwnProperty(i)){var u=c[i];"style"===i?me(e,u):"dangerouslySetInnerHTML"===i?null!=(u=u?u.__html:void 0)&&de(e,u):"children"===i?"string"===typeof u?("textarea"!==n||""!==u)&&fe(e,u):"number"===typeof u&&fe(e,""+u):"suppressContentEditableWarning"!==i&&"suppressHydrationWarning"!==i&&"autoFocus"!==i&&(l.hasOwnProperty(i)?null!=u&&"onScroll"===i&&Ar("scroll",e):null!=u&&b(e,i,u,s))}switch(n){case"input":Y(e),J(e,r,!1);break;case"textarea":Y(e),ie(e);break;case"option":null!=r.value&&e.setAttribute("value",""+V(r.value));break;case"select":e.multiple=!!r.multiple,null!=(i=r.value)?ne(e,!!r.multiple,i,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof a.onClick&&(e.onclick=Jr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Yl(t),null;case 6:if(e&&null!=t.stateNode)zl(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(o(166));if(n=Ko(qo.current),Ko(Yo.current),fo(t)){if(r=t.stateNode,n=t.memoizedProps,r[fa]=t,(i=r.nodeValue!==n)&&null!==(e=no))switch(e.tag){case 3:Zr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Zr(r.nodeValue,n,0!==(1&e.mode))}i&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[fa]=t,t.stateNode=r}return Yl(t),null;case 13:if(Ea(ei),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ao&&null!==ro&&0!==(1&t.mode)&&0===(128&t.flags))po(),go(),t.flags|=98560,i=!1;else if(i=fo(t),null!==r&&null!==r.dehydrated){if(null===e){if(!i)throw Error(o(318));if(!(i=null!==(i=t.memoizedState)?i.dehydrated:null))throw Error(o(317));i[fa]=t}else go(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Yl(t),i=!1}else null!==oo&&(ic(oo),oo=null),i=!0;if(!i)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&ei.current)?0===zs&&(zs=3):hc())),null!==t.updateQueue&&(t.flags|=4),Yl(t),null);case 4:return Go(),_l(e,t),null===e&&Br(t.stateNode.containerInfo),Yl(t),null;case 10:return Oo(t.type._context),Yl(t),null;case 19:if(Ea(ei),null===(i=t.memoizedState))return Yl(t),null;if(r=0!==(128&t.flags),null===(s=i.rendering))if(r)$l(i,!1);else{if(0!==zs||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(s=ti(e))){for(t.flags|=128,$l(i,!1),null!==(r=s.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(i=n).flags&=14680066,null===(s=i.alternate)?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Ta(ei,1&ei.current|2),t.child}e=e.sibling}null!==i.tail&&Ge()>Bs&&(t.flags|=128,r=!0,$l(i,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=ti(s))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),$l(i,!0),null===i.tail&&"hidden"===i.tailMode&&!s.alternate&&!ao)return Yl(t),null}else 2*Ge()-i.renderingStartTime>Bs&&1073741824!==n&&(t.flags|=128,r=!0,$l(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(null!==(n=i.last)?n.sibling=s:t.child=s,i.last=s)}return null!==i.tail?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Ge(),t.sibling=null,n=ei.current,Ta(ei,r?1&n|2:1&n),t):(Yl(t),null);case 22:case 23:return dc(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&_s)&&(Yl(t),6&t.subtreeFlags&&(t.flags|=8192)):Yl(t),null;case 24:case 25:return null}throw Error(o(156,t.tag))}function ql(e,t){switch(to(t),t.tag){case 1:return _a(t.type)&&Pa(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Go(),Ea(ja),Ea(Oa),ri(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Jo(t),null;case 13:if(Ea(ei),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(o(340));go()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Ea(ei),null;case 4:return Go(),null;case 10:return Oo(t.type._context),null;case 22:case 23:return dc(),null;default:return null}}Il=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},_l=function(){},Pl=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,Ko(Yo.current);var o,i=null;switch(n){case"input":a=K(e,a),r=K(e,r),i=[];break;case"select":a=R({},a,{value:void 0}),r=R({},r,{value:void 0}),i=[];break;case"textarea":a=re(e,a),r=re(e,r),i=[];break;default:"function"!==typeof a.onClick&&"function"===typeof r.onClick&&(e.onclick=Jr)}for(u in ye(n,r),n=null,a)if(!r.hasOwnProperty(u)&&a.hasOwnProperty(u)&&null!=a[u])if("style"===u){var s=a[u];for(o in s)s.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else"dangerouslySetInnerHTML"!==u&&"children"!==u&&"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&"autoFocus"!==u&&(l.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var c=r[u];if(s=null!=a?a[u]:void 0,r.hasOwnProperty(u)&&c!==s&&(null!=c||null!=s))if("style"===u)if(s){for(o in s)!s.hasOwnProperty(o)||c&&c.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in c)c.hasOwnProperty(o)&&s[o]!==c[o]&&(n||(n={}),n[o]=c[o])}else n||(i||(i=[]),i.push(u,n)),n=c;else"dangerouslySetInnerHTML"===u?(c=c?c.__html:void 0,s=s?s.__html:void 0,null!=c&&s!==c&&(i=i||[]).push(u,c)):"children"===u?"string"!==typeof c&&"number"!==typeof c||(i=i||[]).push(u,""+c):"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&(l.hasOwnProperty(u)?(null!=c&&"onScroll"===u&&Ar("scroll",e),i||s===c||(i=[])):(i=i||[]).push(u,c))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}},zl=function(e,t,n,r){n!==r&&(t.flags|=4)};var Kl=!1,Xl=!1,Gl="function"===typeof WeakSet?WeakSet:Set,Zl=null;function Jl(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){Cc(e,t,r)}else n.current=null}function es(e,t,n){try{n()}catch(r){Cc(e,t,r)}}var ts=!1;function ns(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var o=a.destroy;a.destroy=void 0,void 0!==o&&es(t,n,o)}a=a.next}while(a!==r)}}function rs(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function as(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function os(e){var t=e.alternate;null!==t&&(e.alternate=null,os(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[fa],delete t[pa],delete t[ha],delete t[ma],delete t[va])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function is(e){return 5===e.tag||3===e.tag||4===e.tag}function ls(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||is(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ss(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Jr));else if(4!==r&&null!==(e=e.child))for(ss(e,t,n),e=e.sibling;null!==e;)ss(e,t,n),e=e.sibling}function cs(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(cs(e,t,n),e=e.sibling;null!==e;)cs(e,t,n),e=e.sibling}var us=null,ds=!1;function fs(e,t,n){for(n=n.child;null!==n;)ps(e,t,n),n=n.sibling}function ps(e,t,n){if(ot&&"function"===typeof ot.onCommitFiberUnmount)try{ot.onCommitFiberUnmount(at,n)}catch(l){}switch(n.tag){case 5:Xl||Jl(n,t);case 6:var r=us,a=ds;us=null,fs(e,t,n),ds=a,null!==(us=r)&&(ds?(e=us,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):us.removeChild(n.stateNode));break;case 18:null!==us&&(ds?(e=us,n=n.stateNode,8===e.nodeType?sa(e.parentNode,n):1===e.nodeType&&sa(e,n),Bt(e)):sa(us,n.stateNode));break;case 4:r=us,a=ds,us=n.stateNode.containerInfo,ds=!0,fs(e,t,n),us=r,ds=a;break;case 0:case 11:case 14:case 15:if(!Xl&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){a=r=r.next;do{var o=a,i=o.destroy;o=o.tag,void 0!==i&&(0!==(2&o)||0!==(4&o))&&es(n,t,i),a=a.next}while(a!==r)}fs(e,t,n);break;case 1:if(!Xl&&(Jl(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){Cc(n,t,l)}fs(e,t,n);break;case 21:fs(e,t,n);break;case 22:1&n.mode?(Xl=(r=Xl)||null!==n.memoizedState,fs(e,t,n),Xl=r):fs(e,t,n);break;default:fs(e,t,n)}}function gs(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Gl),t.forEach((function(t){var r=Oc.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function hs(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var i=e,l=t,s=l;e:for(;null!==s;){switch(s.tag){case 5:us=s.stateNode,ds=!1;break e;case 3:case 4:us=s.stateNode.containerInfo,ds=!0;break e}s=s.return}if(null===us)throw Error(o(160));ps(i,l,a),us=null,ds=!1;var c=a.alternate;null!==c&&(c.return=null),a.return=null}catch(u){Cc(a,t,u)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)ms(t,e),t=t.sibling}function ms(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(hs(t,e),vs(e),4&r){try{ns(3,e,e.return),rs(3,e)}catch(m){Cc(e,e.return,m)}try{ns(5,e,e.return)}catch(m){Cc(e,e.return,m)}}break;case 1:hs(t,e),vs(e),512&r&&null!==n&&Jl(n,n.return);break;case 5:if(hs(t,e),vs(e),512&r&&null!==n&&Jl(n,n.return),32&e.flags){var a=e.stateNode;try{fe(a,"")}catch(m){Cc(e,e.return,m)}}if(4&r&&null!=(a=e.stateNode)){var i=e.memoizedProps,l=null!==n?n.memoizedProps:i,s=e.type,c=e.updateQueue;if(e.updateQueue=null,null!==c)try{"input"===s&&"radio"===i.type&&null!=i.name&&G(a,i),be(s,l);var u=be(s,i);for(l=0;l<c.length;l+=2){var d=c[l],f=c[l+1];"style"===d?me(a,f):"dangerouslySetInnerHTML"===d?de(a,f):"children"===d?fe(a,f):b(a,d,f,u)}switch(s){case"input":Z(a,i);break;case"textarea":oe(a,i);break;case"select":var p=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!i.multiple;var g=i.value;null!=g?ne(a,!!i.multiple,g,!1):p!==!!i.multiple&&(null!=i.defaultValue?ne(a,!!i.multiple,i.defaultValue,!0):ne(a,!!i.multiple,i.multiple?[]:"",!1))}a[pa]=i}catch(m){Cc(e,e.return,m)}}break;case 6:if(hs(t,e),vs(e),4&r){if(null===e.stateNode)throw Error(o(162));a=e.stateNode,i=e.memoizedProps;try{a.nodeValue=i}catch(m){Cc(e,e.return,m)}}break;case 3:if(hs(t,e),vs(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Bt(t.containerInfo)}catch(m){Cc(e,e.return,m)}break;case 4:default:hs(t,e),vs(e);break;case 13:hs(t,e),vs(e),8192&(a=e.child).flags&&(i=null!==a.memoizedState,a.stateNode.isHidden=i,!i||null!==a.alternate&&null!==a.alternate.memoizedState||(Us=Ge())),4&r&&gs(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Xl=(u=Xl)||d,hs(t,e),Xl=u):hs(t,e),vs(e),8192&r){if(u=null!==e.memoizedState,(e.stateNode.isHidden=u)&&!d&&0!==(1&e.mode))for(Zl=e,d=e.child;null!==d;){for(f=Zl=d;null!==Zl;){switch(g=(p=Zl).child,p.tag){case 0:case 11:case 14:case 15:ns(4,p,p.return);break;case 1:Jl(p,p.return);var h=p.stateNode;if("function"===typeof h.componentWillUnmount){r=p,n=p.return;try{t=r,h.props=t.memoizedProps,h.state=t.memoizedState,h.componentWillUnmount()}catch(m){Cc(r,n,m)}}break;case 5:Jl(p,p.return);break;case 22:if(null!==p.memoizedState){ws(f);continue}}null!==g?(g.return=p,Zl=g):ws(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{a=f.stateNode,u?"function"===typeof(i=a.style).setProperty?i.setProperty("display","none","important"):i.display="none":(s=f.stateNode,l=void 0!==(c=f.memoizedProps.style)&&null!==c&&c.hasOwnProperty("display")?c.display:null,s.style.display=he("display",l))}catch(m){Cc(e,e.return,m)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(m){Cc(e,e.return,m)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:hs(t,e),vs(e),4&r&&gs(e);case 21:}}function vs(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(is(n)){var r=n;break e}n=n.return}throw Error(o(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(fe(a,""),r.flags&=-33),cs(e,ls(e),a);break;case 3:case 4:var i=r.stateNode.containerInfo;ss(e,ls(e),i);break;default:throw Error(o(161))}}catch(l){Cc(e,e.return,l)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function ys(e,t,n){Zl=e,bs(e,t,n)}function bs(e,t,n){for(var r=0!==(1&e.mode);null!==Zl;){var a=Zl,o=a.child;if(22===a.tag&&r){var i=null!==a.memoizedState||Kl;if(!i){var l=a.alternate,s=null!==l&&null!==l.memoizedState||Xl;l=Kl;var c=Xl;if(Kl=i,(Xl=s)&&!c)for(Zl=a;null!==Zl;)s=(i=Zl).child,22===i.tag&&null!==i.memoizedState?Ss(a):null!==s?(s.return=i,Zl=s):Ss(a);for(;null!==o;)Zl=o,bs(o,t,n),o=o.sibling;Zl=a,Kl=l,Xl=c}xs(e)}else 0!==(8772&a.subtreeFlags)&&null!==o?(o.return=a,Zl=o):xs(e)}}function xs(e){for(;null!==Zl;){var t=Zl;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Xl||rs(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Xl)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:nl(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;null!==i&&Vo(t,i,r);break;case 3:var l=t.updateQueue;if(null!==l){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Vo(t,l,n)}break;case 5:var s=t.stateNode;if(null===n&&4&t.flags){n=s;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var u=t.alternate;if(null!==u){var d=u.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Bt(f)}}}break;default:throw Error(o(163))}Xl||512&t.flags&&as(t)}catch(p){Cc(t,t.return,p)}}if(t===e){Zl=null;break}if(null!==(n=t.sibling)){n.return=t.return,Zl=n;break}Zl=t.return}}function ws(e){for(;null!==Zl;){var t=Zl;if(t===e){Zl=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Zl=n;break}Zl=t.return}}function Ss(e){for(;null!==Zl;){var t=Zl;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rs(4,t)}catch(s){Cc(t,n,s)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(s){Cc(t,a,s)}}var o=t.return;try{as(t)}catch(s){Cc(t,o,s)}break;case 5:var i=t.return;try{as(t)}catch(s){Cc(t,i,s)}}}catch(s){Cc(t,t.return,s)}if(t===e){Zl=null;break}var l=t.sibling;if(null!==l){l.return=t.return,Zl=l;break}Zl=t.return}}var ks,Cs=Math.ceil,Es=x.ReactCurrentDispatcher,Ts=x.ReactCurrentOwner,Ds=x.ReactCurrentBatchConfig,Os=0,js=null,Ns=null,Is=0,_s=0,Ps=Ca(0),zs=0,Fs=null,Ls=0,Rs=0,Ms=0,As=null,Hs=null,Us=0,Bs=1/0,Ws=null,Vs=!1,$s=null,Ys=null,Qs=!1,qs=null,Ks=0,Xs=0,Gs=null,Zs=-1,Js=0;function ec(){return 0!==(6&Os)?Ge():-1!==Zs?Zs:Zs=Ge()}function tc(e){return 0===(1&e.mode)?1:0!==(2&Os)&&0!==Is?Is&-Is:null!==mo.transition?(0===Js&&(Js=ht()),Js):0!==(e=bt)?e:e=void 0===(e=window.event)?16:Xt(e.type)}function nc(e,t,n,r){if(50<Xs)throw Xs=0,Gs=null,Error(o(185));vt(e,n,r),0!==(2&Os)&&e===js||(e===js&&(0===(2&Os)&&(Rs|=n),4===zs&&lc(e,Is)),rc(e,r),1===n&&0===Os&&0===(1&t.mode)&&(Bs=Ge()+500,Aa&&Ba()))}function rc(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,o=e.pendingLanes;0<o;){var i=31-it(o),l=1<<i,s=a[i];-1===s?0!==(l&n)&&0===(l&r)||(a[i]=pt(l,t)):s<=t&&(e.expiredLanes|=l),o&=~l}}(e,t);var r=ft(e,e===js?Is:0);if(0===r)null!==n&&qe(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&qe(n),1===t)0===e.tag?function(e){Aa=!0,Ua(e)}(sc.bind(null,e)):Ua(sc.bind(null,e)),ia((function(){0===(6&Os)&&Ba()})),n=null;else{switch(xt(r)){case 1:n=Je;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=jc(n,ac.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ac(e,t){if(Zs=-1,Js=0,0!==(6&Os))throw Error(o(327));var n=e.callbackNode;if(Sc()&&e.callbackNode!==n)return null;var r=ft(e,e===js?Is:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=mc(e,r);else{t=r;var a=Os;Os|=2;var i=gc();for(js===e&&Is===t||(Ws=null,Bs=Ge()+500,fc(e,t));;)try{yc();break}catch(s){pc(e,s)}Do(),Es.current=i,Os=a,null!==Ns?t=0:(js=null,Is=0,t=zs)}if(0!==t){if(2===t&&(0!==(a=gt(e))&&(r=a,t=oc(e,a))),1===t)throw n=Fs,fc(e,0),lc(e,r),rc(e,Ge()),n;if(6===t)lc(e,r);else{if(a=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],o=a.getSnapshot;a=a.value;try{if(!lr(o(),a))return!1}catch(l){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)&&(2===(t=mc(e,r))&&(0!==(i=gt(e))&&(r=i,t=oc(e,i))),1===t))throw n=Fs,fc(e,0),lc(e,r),rc(e,Ge()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(o(345));case 2:case 5:wc(e,Hs,Ws);break;case 3:if(lc(e,r),(130023424&r)===r&&10<(t=Us+500-Ge())){if(0!==ft(e,0))break;if(((a=e.suspendedLanes)&r)!==r){ec(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=ra(wc.bind(null,e,Hs,Ws),t);break}wc(e,Hs,Ws);break;case 4:if(lc(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var l=31-it(r);i=1<<l,(l=t[l])>a&&(a=l),r&=~i}if(r=a,10<(r=(120>(r=Ge()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Cs(r/1960))-r)){e.timeoutHandle=ra(wc.bind(null,e,Hs,Ws),r);break}wc(e,Hs,Ws);break;default:throw Error(o(329))}}}return rc(e,Ge()),e.callbackNode===n?ac.bind(null,e):null}function oc(e,t){var n=As;return e.current.memoizedState.isDehydrated&&(fc(e,t).flags|=256),2!==(e=mc(e,t))&&(t=Hs,Hs=n,null!==t&&ic(t)),e}function ic(e){null===Hs?Hs=e:Hs.push.apply(Hs,e)}function lc(e,t){for(t&=~Ms,t&=~Rs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-it(t),r=1<<n;e[n]=-1,t&=~r}}function sc(e){if(0!==(6&Os))throw Error(o(327));Sc();var t=ft(e,0);if(0===(1&t))return rc(e,Ge()),null;var n=mc(e,t);if(0!==e.tag&&2===n){var r=gt(e);0!==r&&(t=r,n=oc(e,r))}if(1===n)throw n=Fs,fc(e,0),lc(e,t),rc(e,Ge()),n;if(6===n)throw Error(o(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,wc(e,Hs,Ws),rc(e,Ge()),null}function cc(e,t){var n=Os;Os|=1;try{return e(t)}finally{0===(Os=n)&&(Bs=Ge()+500,Aa&&Ba())}}function uc(e){null!==qs&&0===qs.tag&&0===(6&Os)&&Sc();var t=Os;Os|=1;var n=Ds.transition,r=bt;try{if(Ds.transition=null,bt=1,e)return e()}finally{bt=r,Ds.transition=n,0===(6&(Os=t))&&Ba()}}function dc(){_s=Ps.current,Ea(Ps)}function fc(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,aa(n)),null!==Ns)for(n=Ns.return;null!==n;){var r=n;switch(to(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&Pa();break;case 3:Go(),Ea(ja),Ea(Oa),ri();break;case 5:Jo(r);break;case 4:Go();break;case 13:case 19:Ea(ei);break;case 10:Oo(r.type._context);break;case 22:case 23:dc()}n=n.return}if(js=e,Ns=e=Pc(e.current,null),Is=_s=t,zs=0,Fs=null,Ms=Rs=Ls=0,Hs=As=null,null!==_o){for(t=0;t<_o.length;t++)if(null!==(r=(n=_o[t]).interleaved)){n.interleaved=null;var a=r.next,o=n.pending;if(null!==o){var i=o.next;o.next=a,r.next=i}n.pending=r}_o=null}return e}function pc(e,t){for(;;){var n=Ns;try{if(Do(),ai.current=Zi,ui){for(var r=li.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}ui=!1}if(ii=0,ci=si=li=null,di=!1,fi=0,Ts.current=null,null===n||null===n.return){zs=1,Fs=t,Ns=null;break}e:{var i=e,l=n.return,s=n,c=t;if(t=Is,s.flags|=32768,null!==c&&"object"===typeof c&&"function"===typeof c.then){var u=c,d=s,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var g=ml(l);if(null!==g){g.flags&=-257,vl(g,l,s,0,t),1&g.mode&&hl(i,u,t),c=u;var h=(t=g).updateQueue;if(null===h){var m=new Set;m.add(c),t.updateQueue=m}else h.add(c);break e}if(0===(1&t)){hl(i,u,t),hc();break e}c=Error(o(426))}else if(ao&&1&s.mode){var v=ml(l);if(null!==v){0===(65536&v.flags)&&(v.flags|=256),vl(v,l,s,0,t),ho(cl(c,s));break e}}i=c=cl(c,s),4!==zs&&(zs=2),null===As?As=[i]:As.push(i),i=l;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t,Bo(i,pl(0,c,t));break e;case 1:s=c;var y=i.type,b=i.stateNode;if(0===(128&i.flags)&&("function"===typeof y.getDerivedStateFromError||null!==b&&"function"===typeof b.componentDidCatch&&(null===Ys||!Ys.has(b)))){i.flags|=65536,t&=-t,i.lanes|=t,Bo(i,gl(i,s,t));break e}}i=i.return}while(null!==i)}xc(n)}catch(x){t=x,Ns===n&&null!==n&&(Ns=n=n.return);continue}break}}function gc(){var e=Es.current;return Es.current=Zi,null===e?Zi:e}function hc(){0!==zs&&3!==zs&&2!==zs||(zs=4),null===js||0===(268435455&Ls)&&0===(268435455&Rs)||lc(js,Is)}function mc(e,t){var n=Os;Os|=2;var r=gc();for(js===e&&Is===t||(Ws=null,fc(e,t));;)try{vc();break}catch(a){pc(e,a)}if(Do(),Os=n,Es.current=r,null!==Ns)throw Error(o(261));return js=null,Is=0,zs}function vc(){for(;null!==Ns;)bc(Ns)}function yc(){for(;null!==Ns&&!Ke();)bc(Ns)}function bc(e){var t=ks(e.alternate,e,_s);e.memoizedProps=e.pendingProps,null===t?xc(e):Ns=t,Ts.current=null}function xc(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=Ql(n,t,_s)))return void(Ns=n)}else{if(null!==(n=ql(n,t)))return n.flags&=32767,void(Ns=n);if(null===e)return zs=6,void(Ns=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Ns=t);Ns=t=e}while(null!==t);0===zs&&(zs=5)}function wc(e,t,n){var r=bt,a=Ds.transition;try{Ds.transition=null,bt=1,function(e,t,n,r){do{Sc()}while(null!==qs);if(0!==(6&Os))throw Error(o(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(o(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-it(n),o=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~o}}(e,i),e===js&&(Ns=js=null,Is=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||Qs||(Qs=!0,jc(tt,(function(){return Sc(),null}))),i=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||i){i=Ds.transition,Ds.transition=null;var l=bt;bt=1;var s=Os;Os|=4,Ts.current=null,function(e,t){if(ea=Vt,pr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch(w){n=null;break e}var l=0,s=-1,c=-1,u=0,d=0,f=e,p=null;t:for(;;){for(var g;f!==n||0!==a&&3!==f.nodeType||(s=l+a),f!==i||0!==r&&3!==f.nodeType||(c=l+r),3===f.nodeType&&(l+=f.nodeValue.length),null!==(g=f.firstChild);)p=f,f=g;for(;;){if(f===e)break t;if(p===n&&++u===a&&(s=l),p===i&&++d===r&&(c=l),null!==(g=f.nextSibling))break;p=(f=p).parentNode}f=g}n=-1===s||-1===c?null:{start:s,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(ta={focusedElem:e,selectionRange:n},Vt=!1,Zl=t;null!==Zl;)if(e=(t=Zl).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Zl=e;else for(;null!==Zl;){t=Zl;try{var h=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==h){var m=h.memoizedProps,v=h.memoizedState,y=t.stateNode,b=y.getSnapshotBeforeUpdate(t.elementType===t.type?m:nl(t.type,m),v);y.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var x=t.stateNode.containerInfo;1===x.nodeType?x.textContent="":9===x.nodeType&&x.documentElement&&x.removeChild(x.documentElement);break;default:throw Error(o(163))}}catch(w){Cc(t,t.return,w)}if(null!==(e=t.sibling)){e.return=t.return,Zl=e;break}Zl=t.return}h=ts,ts=!1}(e,n),ms(n,e),gr(ta),Vt=!!ea,ta=ea=null,e.current=n,ys(n,e,a),Xe(),Os=s,bt=l,Ds.transition=i}else e.current=n;if(Qs&&(Qs=!1,qs=e,Ks=a),i=e.pendingLanes,0===i&&(Ys=null),function(e){if(ot&&"function"===typeof ot.onCommitFiberRoot)try{ot.onCommitFiberRoot(at,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),rc(e,Ge()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if(Vs)throw Vs=!1,e=$s,$s=null,e;0!==(1&Ks)&&0!==e.tag&&Sc(),i=e.pendingLanes,0!==(1&i)?e===Gs?Xs++:(Xs=0,Gs=e):Xs=0,Ba()}(e,t,n,r)}finally{Ds.transition=a,bt=r}return null}function Sc(){if(null!==qs){var e=xt(Ks),t=Ds.transition,n=bt;try{if(Ds.transition=null,bt=16>e?16:e,null===qs)var r=!1;else{if(e=qs,qs=null,Ks=0,0!==(6&Os))throw Error(o(331));var a=Os;for(Os|=4,Zl=e.current;null!==Zl;){var i=Zl,l=i.child;if(0!==(16&Zl.flags)){var s=i.deletions;if(null!==s){for(var c=0;c<s.length;c++){var u=s[c];for(Zl=u;null!==Zl;){var d=Zl;switch(d.tag){case 0:case 11:case 15:ns(8,d,i)}var f=d.child;if(null!==f)f.return=d,Zl=f;else for(;null!==Zl;){var p=(d=Zl).sibling,g=d.return;if(os(d),d===u){Zl=null;break}if(null!==p){p.return=g,Zl=p;break}Zl=g}}}var h=i.alternate;if(null!==h){var m=h.child;if(null!==m){h.child=null;do{var v=m.sibling;m.sibling=null,m=v}while(null!==m)}}Zl=i}}if(0!==(2064&i.subtreeFlags)&&null!==l)l.return=i,Zl=l;else e:for(;null!==Zl;){if(0!==(2048&(i=Zl).flags))switch(i.tag){case 0:case 11:case 15:ns(9,i,i.return)}var y=i.sibling;if(null!==y){y.return=i.return,Zl=y;break e}Zl=i.return}}var b=e.current;for(Zl=b;null!==Zl;){var x=(l=Zl).child;if(0!==(2064&l.subtreeFlags)&&null!==x)x.return=l,Zl=x;else e:for(l=b;null!==Zl;){if(0!==(2048&(s=Zl).flags))try{switch(s.tag){case 0:case 11:case 15:rs(9,s)}}catch(S){Cc(s,s.return,S)}if(s===l){Zl=null;break e}var w=s.sibling;if(null!==w){w.return=s.return,Zl=w;break e}Zl=s.return}}if(Os=a,Ba(),ot&&"function"===typeof ot.onPostCommitFiberRoot)try{ot.onPostCommitFiberRoot(at,e)}catch(S){}r=!0}return r}finally{bt=n,Ds.transition=t}}return!1}function kc(e,t,n){e=Ho(e,t=pl(0,t=cl(n,t),1),1),t=ec(),null!==e&&(vt(e,1,t),rc(e,t))}function Cc(e,t,n){if(3===e.tag)kc(e,e,n);else for(;null!==t;){if(3===t.tag){kc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Ys||!Ys.has(r))){t=Ho(t,e=gl(t,e=cl(n,e),1),1),e=ec(),null!==t&&(vt(t,1,e),rc(t,e));break}}t=t.return}}function Ec(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=ec(),e.pingedLanes|=e.suspendedLanes&n,js===e&&(Is&n)===n&&(4===zs||3===zs&&(130023424&Is)===Is&&500>Ge()-Us?fc(e,0):Ms|=n),rc(e,t)}function Tc(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ut,0===(130023424&(ut<<=1))&&(ut=4194304)));var n=ec();null!==(e=Fo(e,t))&&(vt(e,t,n),rc(e,n))}function Dc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Tc(e,n)}function Oc(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(o(314))}null!==r&&r.delete(t),Tc(e,n)}function jc(e,t){return Qe(e,t)}function Nc(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ic(e,t,n,r){return new Nc(e,t,n,r)}function _c(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Pc(e,t){var n=e.alternate;return null===n?((n=Ic(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function zc(e,t,n,r,a,i){var l=2;if(r=e,"function"===typeof e)_c(e)&&(l=1);else if("string"===typeof e)l=5;else e:switch(e){case k:return Fc(n.children,a,i,t);case C:l=8,a|=8;break;case E:return(e=Ic(12,n,t,2|a)).elementType=E,e.lanes=i,e;case j:return(e=Ic(13,n,t,a)).elementType=j,e.lanes=i,e;case N:return(e=Ic(19,n,t,a)).elementType=N,e.lanes=i,e;case P:return Lc(n,a,i,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case T:l=10;break e;case D:l=9;break e;case O:l=11;break e;case I:l=14;break e;case _:l=16,r=null;break e}throw Error(o(130,null==e?e:typeof e,""))}return(t=Ic(l,n,t,a)).elementType=e,t.type=r,t.lanes=i,t}function Fc(e,t,n,r){return(e=Ic(7,e,r,t)).lanes=n,e}function Lc(e,t,n,r){return(e=Ic(22,e,r,t)).elementType=P,e.lanes=n,e.stateNode={isHidden:!1},e}function Rc(e,t,n){return(e=Ic(6,e,null,t)).lanes=n,e}function Mc(e,t,n){return(t=Ic(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Ac(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=mt(0),this.expirationTimes=mt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=mt(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Hc(e,t,n,r,a,o,i,l,s){return e=new Ac(e,t,n,l,s),1===t?(t=1,!0===o&&(t|=8)):t=0,o=Ic(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ro(o),e}function Uc(e){if(!e)return Da;e:{if(Be(e=e._reactInternals)!==e||1!==e.tag)throw Error(o(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(_a(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(o(171))}if(1===e.tag){var n=e.type;if(_a(n))return Fa(e,n,t)}return t}function Bc(e,t,n,r,a,o,i,l,s){return(e=Hc(n,r,!0,e,0,o,0,l,s)).context=Uc(null),n=e.current,(o=Ao(r=ec(),a=tc(n))).callback=void 0!==t&&null!==t?t:null,Ho(n,o,a),e.current.lanes=a,vt(e,a,r),rc(e,r),e}function Wc(e,t,n,r){var a=t.current,o=ec(),i=tc(a);return n=Uc(n),null===t.context?t.context=n:t.pendingContext=n,(t=Ao(o,i)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Ho(a,t,i))&&(nc(e,a,i,o),Uo(e,a,i)),i}function Vc(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function $c(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Yc(e,t){$c(e,t),(e=e.alternate)&&$c(e,t)}ks=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||ja.current)bl=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return bl=!1,function(e,t,n){switch(t.tag){case 3:jl(t),go();break;case 5:Zo(t);break;case 1:_a(t.type)&&La(t);break;case 4:Xo(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;Ta(ko,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Ta(ei,1&ei.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Rl(e,t,n):(Ta(ei,1&ei.current),null!==(e=Vl(e,t,n))?e.sibling:null);Ta(ei,1&ei.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Bl(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),Ta(ei,ei.current),r)break;return null;case 22:case 23:return t.lanes=0,Cl(e,t,n)}return Vl(e,t,n)}(e,t,n);bl=0!==(131072&e.flags)}else bl=!1,ao&&0!==(1048576&t.flags)&&Ja(t,Ya,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Wl(e,t),e=t.pendingProps;var a=Ia(t,Oa.current);No(t,n),a=mi(null,t,r,e,a,n);var i=vi();return t.flags|=1,"object"===typeof a&&null!==a&&"function"===typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,_a(r)?(i=!0,La(t)):i=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Ro(t),a.updater=al,t.stateNode=a,a._reactInternals=t,sl(t,r,e,n),t=Ol(null,t,r,!0,i,n)):(t.tag=0,ao&&i&&eo(t),xl(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Wl(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"===typeof e)return _c(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===O)return 11;if(e===I)return 14}return 2}(r),e=nl(r,e),a){case 0:t=Tl(null,t,r,e,n);break e;case 1:t=Dl(null,t,r,e,n);break e;case 11:t=wl(null,t,r,e,n);break e;case 14:t=Sl(null,t,r,nl(r.type,e),n);break e}throw Error(o(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,Tl(e,t,r,a=t.elementType===r?a:nl(r,a),n);case 1:return r=t.type,a=t.pendingProps,Dl(e,t,r,a=t.elementType===r?a:nl(r,a),n);case 3:e:{if(jl(t),null===e)throw Error(o(387));r=t.pendingProps,a=(i=t.memoizedState).element,Mo(e,t),Wo(t,r,null,n);var l=t.memoizedState;if(r=l.element,i.isDehydrated){if(i={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=i,t.memoizedState=i,256&t.flags){t=Nl(e,t,r,n,a=cl(Error(o(423)),t));break e}if(r!==a){t=Nl(e,t,r,n,a=cl(Error(o(424)),t));break e}for(ro=ca(t.stateNode.containerInfo.firstChild),no=t,ao=!0,oo=null,n=So(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(go(),r===a){t=Vl(e,t,n);break e}xl(e,t,r,n)}t=t.child}return t;case 5:return Zo(t),null===e&&co(t),r=t.type,a=t.pendingProps,i=null!==e?e.memoizedProps:null,l=a.children,na(r,a)?l=null:null!==i&&na(r,i)&&(t.flags|=32),El(e,t),xl(e,t,l,n),t.child;case 6:return null===e&&co(t),null;case 13:return Rl(e,t,n);case 4:return Xo(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=wo(t,null,r,n):xl(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,wl(e,t,r,a=t.elementType===r?a:nl(r,a),n);case 7:return xl(e,t,t.pendingProps,n),t.child;case 8:case 12:return xl(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,i=t.memoizedProps,l=a.value,Ta(ko,r._currentValue),r._currentValue=l,null!==i)if(lr(i.value,l)){if(i.children===a.children&&!ja.current){t=Vl(e,t,n);break e}}else for(null!==(i=t.child)&&(i.return=t);null!==i;){var s=i.dependencies;if(null!==s){l=i.child;for(var c=s.firstContext;null!==c;){if(c.context===r){if(1===i.tag){(c=Ao(-1,n&-n)).tag=2;var u=i.updateQueue;if(null!==u){var d=(u=u.shared).pending;null===d?c.next=c:(c.next=d.next,d.next=c),u.pending=c}}i.lanes|=n,null!==(c=i.alternate)&&(c.lanes|=n),jo(i.return,n,t),s.lanes|=n;break}c=c.next}}else if(10===i.tag)l=i.type===t.type?null:i.child;else if(18===i.tag){if(null===(l=i.return))throw Error(o(341));l.lanes|=n,null!==(s=l.alternate)&&(s.lanes|=n),jo(l,n,t),l=i.sibling}else l=i.child;if(null!==l)l.return=i;else for(l=i;null!==l;){if(l===t){l=null;break}if(null!==(i=l.sibling)){i.return=l.return,l=i;break}l=l.return}i=l}xl(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,No(t,n),r=r(a=Io(a)),t.flags|=1,xl(e,t,r,n),t.child;case 14:return a=nl(r=t.type,t.pendingProps),Sl(e,t,r,a=nl(r.type,a),n);case 15:return kl(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:nl(r,a),Wl(e,t),t.tag=1,_a(r)?(e=!0,La(t)):e=!1,No(t,n),il(t,r,a),sl(t,r,a,n),Ol(null,t,r,!0,e,n);case 19:return Bl(e,t,n);case 22:return Cl(e,t,n)}throw Error(o(156,t.tag))};var Qc="function"===typeof reportError?reportError:function(e){console.error(e)};function qc(e){this._internalRoot=e}function Kc(e){this._internalRoot=e}function Xc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Gc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Zc(){}function Jc(e,t,n,r,a){var o=n._reactRootContainer;if(o){var i=o;if("function"===typeof a){var l=a;a=function(){var e=Vc(i);l.call(e)}}Wc(t,i,e,a)}else i=function(e,t,n,r,a){if(a){if("function"===typeof r){var o=r;r=function(){var e=Vc(i);o.call(e)}}var i=Bc(t,r,e,0,null,!1,0,"",Zc);return e._reactRootContainer=i,e[ga]=i.current,Br(8===e.nodeType?e.parentNode:e),uc(),i}for(;a=e.lastChild;)e.removeChild(a);if("function"===typeof r){var l=r;r=function(){var e=Vc(s);l.call(e)}}var s=Hc(e,0,!1,null,0,!1,0,"",Zc);return e._reactRootContainer=s,e[ga]=s.current,Br(8===e.nodeType?e.parentNode:e),uc((function(){Wc(t,s,n,r)})),s}(n,t,e,a,r);return Vc(i)}Kc.prototype.render=qc.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(o(409));Wc(e,t,null,null)},Kc.prototype.unmount=qc.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;uc((function(){Wc(null,e,null,null)})),t[ga]=null}},Kc.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ct();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Pt.length&&0!==t&&t<Pt[n].priority;n++);Pt.splice(n,0,e),0===n&&Rt(e)}},wt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(yt(t,1|n),rc(t,Ge()),0===(6&Os)&&(Bs=Ge()+500,Ba()))}break;case 13:uc((function(){var t=Fo(e,1);if(null!==t){var n=ec();nc(t,e,1,n)}})),Yc(e,1)}},St=function(e){if(13===e.tag){var t=Fo(e,134217728);if(null!==t)nc(t,e,134217728,ec());Yc(e,134217728)}},kt=function(e){if(13===e.tag){var t=tc(e),n=Fo(e,t);if(null!==n)nc(n,e,t,ec());Yc(e,t)}},Ct=function(){return bt},Et=function(e,t){var n=bt;try{return bt=e,t()}finally{bt=n}},Se=function(e,t,n){switch(t){case"input":if(Z(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=wa(r);if(!a)throw Error(o(90));Q(r),Z(r,a)}}}break;case"textarea":oe(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Oe=cc,je=uc;var eu={usingClientEntryPoint:!1,Events:[ba,xa,wa,Te,De,cc]},tu={findFiberByHostInstance:ya,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nu={bundleType:tu.bundleType,version:tu.version,rendererPackageName:tu.rendererPackageName,rendererConfig:tu.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:x.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=$e(e))?null:e.stateNode},findFiberByHostInstance:tu.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var ru=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ru.isDisabled&&ru.supportsFiber)try{at=ru.inject(nu),ot=ru}catch(ue){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=eu,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Xc(t))throw Error(o(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:S,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Xc(e))throw Error(o(299));var n=!1,r="",a=Qc;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=Hc(e,1,!1,null,0,n,0,r,a),e[ga]=t.current,Br(8===e.nodeType?e.parentNode:e),new qc(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(o(188));throw e=Object.keys(e).join(","),Error(o(268,e))}return e=null===(e=$e(t))?null:e.stateNode},t.flushSync=function(e){return uc(e)},t.hydrate=function(e,t,n){if(!Gc(t))throw Error(o(200));return Jc(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Xc(e))throw Error(o(405));var r=null!=n&&n.hydratedSources||null,a=!1,i="",l=Qc;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(i=n.identifierPrefix),void 0!==n.onRecoverableError&&(l=n.onRecoverableError)),t=Bc(t,null,e,1,null!=n?n:null,a,0,i,l),e[ga]=t.current,Br(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new Kc(t)},t.render=function(e,t,n){if(!Gc(t))throw Error(o(200));return Jc(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Gc(e))throw Error(o(40));return!!e._reactRootContainer&&(uc((function(){Jc(null,null,e,!1,(function(){e._reactRootContainer=null,e[ga]=null}))})),!0)},t.unstable_batchedUpdates=cc,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Gc(n))throw Error(o(200));if(null==e||void 0===e._reactInternals)throw Error(o(38));return Jc(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},844:e=>{e.exports={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}},853:(e,t,n)=>{"use strict";e.exports=n(234)},950:(e,t,n)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(730)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{"use strict";var e={};n.r(e),n.d(e,{FILE:()=>St,HTML:()=>Et,TEXT:()=>Ct,URL:()=>kt});var t=n(43),r=n(391);function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function o(e){var t=function(e,t){if("object"!=a(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=a(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==a(t)?t:t+""}function i(e,t,n){return(t=o(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){i(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const c={rtl:{spacing:{inlineStart:"margin-inline-start",inlineEnd:"margin-inline-end",blockStart:"margin-block-start",blockEnd:"margin-block-end"},borderRadius:{startStart:"border-start-start-radius",startEnd:"border-start-end-radius",endStart:"border-end-start-radius",endEnd:"border-end-end-radius"},textAlign:{start:"text-align: start",end:"text-align: end",center:"text-align: center"},float:{start:"float: inline-start",end:"float: inline-end"}},arabic:{fontFamily:{modern:["Noto Sans Arabic","IBM Plex Sans Arabic","Tajawal","sans-serif"],traditional:["Amiri","Scheherazade New","Noto Naskh Arabic","serif"],display:["Cairo","Almarai","Markazi Text","serif"],mono:["Noto Sans Mono","IBM Plex Mono","Courier New","monospace"]},fontWeight:{light:300,regular:400,medium:500,semibold:600,bold:700,extrabold:800},lineHeight:{tight:1.4,normal:1.6,relaxed:1.8,loose:2},letterSpacing:{normal:"0em",wide:"0.025em"},direction:{rtl:"rtl",ltr:"ltr",auto:"auto"},fontFeatureSettings:{contextual:'"calt" 1',ligatures:'"liga" 1, "clig" 1',kerning:'"kern" 1',arabicForms:'"init" 1, "medi" 1, "fina" 1, "isol" 1'}},gulf:{colors:{traditional:{gold:"#D4AF37",pearl:"#F8F6F0",turquoise:"#40E0D0",coral:"#FF7F50",sand:"#F4A460"},modern:{teal:"#008080",navy:"#1B365D",cream:"#F5F5DC",bronze:"#CD7F32",sage:"#9CAF88"}},typography:{preferred:["Tajawal","Cairo","Almarai","Noto Sans Arabic"],fontSize:{xs:"0.8rem",sm:"0.95rem",base:"1.1rem",lg:"1.3rem",xl:"1.5rem"}},layout:{spacing:{comfortable:"1.5rem",generous:"2rem"},contentWidth:{narrow:"45rem",standard:"60rem",wide:"80rem"}}},culturalColors:{islamic:{sacred:"#006633",peace:"#FFFFFF",wisdom:"#000080",earth:"#8B4513",gold:"#FFD700"},gulfRegion:{hospitality:"#D4AF37",heritage:"#8B4513",sea:"#008B8B",desert:"#F4A460",sky:"#87CEEB"}},breakpoints:{xs:"320px",sm:"640px",md:"768px",lg:"1024px",xl:"1280px","2xl":"1536px",arabicMobile:"375px",arabicTablet:"768px",arabicDesktop:"1200px"},motion:{respectful:{duration:"300ms",easing:"ease-out"},elegant:{duration:"400ms",easing:"cubic-bezier(0.25, 0.46, 0.45, 0.94)"}},accessibility:{focus:{rtl:{outline:"2px solid #3B82F6",outlineOffset:"2px",borderRadius:"4px"}},highContrast:{textOnLight:"#000000",textOnDark:"#FFFFFF",border:"#000000"}},localization:{numerals:{western:"0123456789",arabicIndic:"\u0660\u0661\u0662\u0663\u0664\u0665\u0666\u0667\u0668\u0669",persian:"\u06f0\u06f1\u06f2\u06f3\u06f4\u06f5\u06f6\u06f7\u06f8\u06f9"},dateFormat:{gulf:"DD/MM/YYYY",islamic:"Hijri calendar support",western:"MM/DD/YYYY"},currency:{aed:"\u062f.\u0625",sar:"\u0631.\u0633",kwd:"\u062f.\u0643",qar:"\u0631.\u0642",bhd:"\u062f.\u0628",omr:"\u0631.\u0639"}}},u={isRTL:function(){return["ar","he","fa","ur","ku","dv"].includes((arguments.length>0&&void 0!==arguments[0]?arguments[0]:"en").toLowerCase().substring(0,2))},getFontFamily:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"en",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"modern";return u.isRTL(e)?c.arabic.fontFamily[t]||c.arabic.fontFamily.modern:["Inter","system-ui","sans-serif"]},convertNumerals:function(e){return"arabicIndic"===(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"arabicIndic")?e.replace(/[0-9]/g,(e=>"\u0660\u0661\u0662\u0663\u0664\u0665\u0666\u0667\u0668\u0669"[e])):e},getCulturalColor:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"modern";return c.gulf.colors[t]||c.gulf.colors.modern},getLogicalSpacing:function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr";const r={"margin-left":"rtl"===n?"margin-inline-end":"margin-inline-start","margin-right":"rtl"===n?"margin-inline-start":"margin-inline-end","padding-left":"rtl"===n?"padding-inline-end":"padding-inline-start","padding-right":"rtl"===n?"padding-inline-start":"padding-inline-end"};return{[r[e]||e]:t}}},d={duration:{instant:"0ms",fast:"150ms",normal:"200ms",slow:"300ms",slower:"500ms",slowest:"800ms",glowPulse:"3s",reduced:"0.01ms",respectful:"250ms"},easing:{linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",slideSpring:"cubic-bezier(0.4, 0, 0.2, 1)",bounceOut:"cubic-bezier(0.34, 1.56, 0.64, 1)",backOut:"cubic-bezier(0.34, 1.26, 0.64, 1)",gentle:"cubic-bezier(0.25, 0.46, 0.45, 0.94)",smooth:"cubic-bezier(0.4, 0.0, 0.2, 1)",elegant:"cubic-bezier(0.25, 0.46, 0.45, 0.94)"},animations:{fadeIn:{keyframes:{from:{opacity:0},to:{opacity:1}},duration:"var(--motion-duration-normal)",easing:"var(--motion-easing-ease-out)",fillMode:"both"},fadeOut:{keyframes:{from:{opacity:1},to:{opacity:0}},duration:"var(--motion-duration-normal)",easing:"var(--motion-easing-ease-in)",fillMode:"both"},slideInUp:{keyframes:{from:{transform:"translateY(100%)",opacity:0},to:{transform:"translateY(0)",opacity:1}},duration:"var(--motion-duration-normal)",easing:"var(--motion-easing-slide-spring)",fillMode:"both"},slideInDown:{keyframes:{from:{transform:"translateY(-100%)",opacity:0},to:{transform:"translateY(0)",opacity:1}},duration:"var(--motion-duration-normal)",easing:"var(--motion-easing-slide-spring)",fillMode:"both"},slideInStart:{keyframes:{from:{transform:"translateX(-100%)",opacity:0},to:{transform:"translateX(0)",opacity:1}},duration:"var(--motion-duration-normal)",easing:"var(--motion-easing-slide-spring)",fillMode:"both"},slideInEnd:{keyframes:{from:{transform:"translateX(100%)",opacity:0},to:{transform:"translateX(0)",opacity:1}},duration:"var(--motion-duration-normal)",easing:"var(--motion-easing-slide-spring)",fillMode:"both"},scaleIn:{keyframes:{from:{transform:"scale(0.9)",opacity:0},to:{transform:"scale(1)",opacity:1}},duration:"var(--motion-duration-fast)",easing:"var(--motion-easing-ease-out)",fillMode:"both"},scaleOut:{keyframes:{from:{transform:"scale(1)",opacity:1},to:{transform:"scale(0.9)",opacity:0}},duration:"var(--motion-duration-fast)",easing:"var(--motion-easing-ease-in)",fillMode:"both"},glowPulse:{keyframes:{"0%":{boxShadow:"0 0 5px rgba(95, 185, 255, 0.5)"},"50%":{boxShadow:"0 0 20px rgba(95, 185, 255, 0.8)"},"100%":{boxShadow:"0 0 5px rgba(95, 185, 255, 0.5)"}},duration:"var(--motion-duration-glow-pulse)",easing:"var(--motion-easing-ease-in-out)",iterationCount:"infinite"},gentleBounce:{keyframes:{"0%":{transform:"translateY(0)"},"25%":{transform:"translateY(-2px)"},"50%":{transform:"translateY(0)"},"75%":{transform:"translateY(-1px)"},"100%":{transform:"translateY(0)"}},duration:"var(--motion-duration-slow)",easing:"var(--motion-easing-ease-in-out)",iterationCount:"infinite"},focusRing:{keyframes:{from:{outline:"2px solid transparent",outlineOffset:"2px"},to:{outline:"2px solid var(--color-focus)",outlineOffset:"2px"}},duration:"var(--motion-duration-fast)",easing:"var(--motion-easing-ease-out)",fillMode:"both"}},transitions:{all:"all var(--motion-duration-normal) var(--motion-easing-ease)",colors:"color var(--motion-duration-fast) var(--motion-easing-ease), background-color var(--motion-duration-fast) var(--motion-easing-ease), border-color var(--motion-duration-fast) var(--motion-easing-ease)",opacity:"opacity var(--motion-duration-normal) var(--motion-easing-ease)",transform:"transform var(--motion-duration-normal) var(--motion-easing-slide-spring)",button:"all var(--motion-duration-fast) var(--motion-easing-ease)",input:"border-color var(--motion-duration-fast) var(--motion-easing-ease), box-shadow var(--motion-duration-fast) var(--motion-easing-ease)",link:"color var(--motion-duration-fast) var(--motion-easing-ease)",layout:"width var(--motion-duration-normal) var(--motion-easing-slide-spring), height var(--motion-duration-normal) var(--motion-easing-slide-spring)",spacing:"margin var(--motion-duration-normal) var(--motion-easing-ease), padding var(--motion-duration-normal) var(--motion-easing-ease)",reduced:"none",focus:"outline var(--motion-duration-fast) var(--motion-easing-ease), outline-offset var(--motion-duration-fast) var(--motion-easing-ease)"},preferences:{respectReducedMotion:!0,default:{duration:"var(--motion-duration-normal)",easing:"var(--motion-easing-ease)",delay:"0ms"},reduced:{duration:"var(--motion-duration-reduced)",easing:"var(--motion-easing-linear)",delay:"0ms"},enhanced:{duration:"var(--motion-duration-slow)",easing:"var(--motion-easing-elegant)",delay:"50ms"}},cultural:{gulf:{timingMultiplier:1.3,respectfulDelay:"50ms",elegantDuration:"400ms",easing:"cubic-bezier(0.25, 0.46, 0.45, 0.94)"},levant:{timingMultiplier:1.1,respectfulDelay:"30ms",elegantDuration:"300ms",easing:"cubic-bezier(0.4, 0.0, 0.2, 1)"},maghreb:{timingMultiplier:1,respectfulDelay:"20ms",elegantDuration:"250ms",easing:"ease-out"},global:{timingMultiplier:1,respectfulDelay:"0ms",elegantDuration:"200ms",easing:"ease"}},performance:{high:{duration:"var(--motion-duration-normal)",easing:"var(--motion-easing-elegant)",gpuAccelerated:!0},balanced:{duration:"var(--motion-duration-fast)",easing:"var(--motion-easing-ease-out)",gpuAccelerated:!0},battery:{duration:"var(--motion-duration-fast)",easing:"linear",gpuAccelerated:!1}},stagger:{children:"50ms",items:"100ms",cards:"150ms",sections:"200ms",cultural:{gulf:"80ms",levant:"60ms",maghreb:"50ms",global:"40ms"}},loading:{spinner:{keyframes:{from:{transform:"rotate(0deg)"},to:{transform:"rotate(360deg)"}},duration:"1s",easing:"linear",iterationCount:"infinite"},pulse:{keyframes:{"0%":{opacity:1},"50%":{opacity:.5},"100%":{opacity:1}},duration:"2s",easing:"ease-in-out",iterationCount:"infinite"},progressBar:{keyframes:{from:{transform:"translateX(-100%)"},to:{transform:"translateX(100%)"}},duration:"2s",easing:"ease-in-out",iterationCount:"infinite"}}},f={prefersReducedMotion:()=>window.matchMedia("(prefers-reduced-motion: reduce)").matches,getAnimation:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"default";return f.prefersReducedMotion()||"reduced"===t?s(s({},d.animations[e]),{},{duration:d.duration.reduced,easing:d.easing.linear}):d.animations[e]},createAnimation:function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ease",r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"0ms",a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"1",o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:"both";const i=f.prefersReducedMotion()?d.duration.reduced:t;return"".concat(e," ").concat(i," ").concat(n," ").concat(r," ").concat(a," ").concat(o)},createTransition:function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ease",r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"0ms";const a=f.prefersReducedMotion()?d.duration.reduced:t;return"".concat(e," ").concat(a," ").concat(n," ").concat(r)},applyStagger:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;e.forEach(((e,n)=>{e.style.animationDelay="".concat(n*t,"ms")}))}},p=s(s(s(s({},{colors:{primary:{50:"#f0f9ff",100:"#e0f2fe",200:"#bae6fd",300:"#7dd3fc",400:"#38bdf8",500:"#0ea5e9",600:"#0284c7",700:"#0369a1",800:"#075985",900:"#0c4a6e",950:"#082f49",DEFAULT:"#2E2E2E"},accent:{50:"#eff6ff",100:"#dbeafe",200:"#bfdbfe",300:"#93c5fd",400:"#60a5fa",500:"#3b82f6",600:"#2563eb",700:"#1d4ed8",800:"#1e40af",900:"#1e3a8a",950:"#172554",DEFAULT:"#5FB9FF"},warning:{50:"#fffbeb",100:"#fef3c7",200:"#fde68a",300:"#fcd34d",400:"#fbbf24",500:"#f59e0b",600:"#d97706",700:"#b45309",800:"#92400e",900:"#78350f",950:"#451a03",DEFAULT:"#F6C343"},danger:{50:"#fef2f2",100:"#fee2e2",200:"#fecaca",300:"#fca5a5",400:"#f87171",500:"#ef4444",600:"#dc2626",700:"#b91c1c",800:"#991b1b",900:"#7f1d1d",950:"#450a0a",DEFAULT:"#FF6161"},critical:{50:"#fdf4ff",100:"#fae8ff",200:"#f5d0fe",300:"#f0abfc",400:"#e879f9",500:"#d946ef",600:"#c026d3",700:"#a21caf",800:"#86198f",900:"#701a75",950:"#4a044e",DEFAULT:"#D45AFF"},success:{50:"#f0fdf4",100:"#dcfce7",200:"#bbf7d0",300:"#86efac",400:"#4ade80",500:"#22c55e",600:"#16a34a",700:"#15803d",800:"#166534",900:"#14532d",950:"#052e16",DEFAULT:"#4CC28E"},background:{light:"#FAFAFA",dark:"#181818",paper:"#FFFFFF",elevated:"#F5F5F5"},text:{primary:"#1F2937",secondary:"#6B7280",tertiary:"#9CA3AF",inverse:"#FFFFFF",disabled:"#D1D5DB"},border:{light:"#E5E7EB",medium:"#D1D5DB",dark:"#9CA3AF",focus:"#3B82F6"}},typography:{fontFamily:{base:["Inter","Satoshi","system-ui","sans-serif"],arabic:["Noto Sans Arabic","Amiri","serif"],dyslexic:["OpenDyslexic","Comic Sans MS","sans-serif"],mono:["JetBrains Mono","Consolas","monospace"]},fontSize:{xs:"0.75rem",sm:"0.875rem",base:"1rem",lg:"1.125rem",xl:"1.25rem","2xl":"1.5rem","3xl":"1.875rem","4xl":"2.25rem","5xl":"3rem","6xl":"3.75rem","7xl":"4.5rem","8xl":"6rem","9xl":"8rem"},fontWeight:{thin:100,extralight:200,light:300,normal:400,medium:500,semibold:600,bold:700,extrabold:800,black:900,header:600,body:400},lineHeight:{none:1,tight:1.25,snug:1.375,normal:1.5,relaxed:1.625,loose:2},letterSpacing:{tighter:"-0.05em",tight:"-0.025em",normal:"0em",wide:"0.025em",wider:"0.05em",widest:"0.1em"}},spacing:{0:"0px",1:"0.25rem",2:"0.5rem",3:"0.75rem",4:"1rem",5:"1.25rem",6:"1.5rem",8:"2rem",10:"2.5rem",12:"3rem",16:"4rem",20:"5rem",24:"6rem",32:"8rem",40:"10rem",48:"12rem",56:"14rem",64:"16rem"},borderRadius:{none:"0px",sm:"0.125rem",DEFAULT:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px",button:"0.5rem",input:"1rem"},boxShadow:{sm:"0 1px 2px 0 rgb(0 0 0 / 0.05)",DEFAULT:"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",md:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",lg:"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",xl:"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)","2xl":"0 25px 50px -12px rgb(0 0 0 / 0.25)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",none:"none"},zIndex:{auto:"auto",0:0,10:10,20:20,30:30,40:40,50:50,dropdown:1e3,sticky:1020,fixed:1030,modal:1040,popover:1050,tooltip:1060,toast:1070}}),{contrast:{textOnLight:{primary:"#000000",secondary:"#1F2937",tertiary:"#374151"},textOnDark:{primary:"#FFFFFF",secondary:"#F9FAFB",tertiary:"#E5E7EB"},interactive:{primary:"#1D4ED8",hover:"#1E40AF",active:"#1E3A8A",disabled:"#9CA3AF"},status:{error:"#DC2626",warning:"#D97706",success:"#059669",info:"#0284C7"}},focus:{ring:{width:"2px",style:"solid",color:"#3B82F6",offset:"2px"},highContrast:{width:"3px",style:"solid",color:"#000000",offset:"1px",background:"#FFFF00"}},touchTarget:{minimum:"44px",recommended:"48px",comfortable:"56px"},motion:{reduced:{duration:"0.01ms",easing:"linear"},standard:{duration:"200ms",easing:"cubic-bezier(0.4, 0, 0.2, 1)"},enhanced:{duration:"300ms",easing:"cubic-bezier(0.4, 0, 0.2, 1)"}},screenReader:{visuallyHidden:{position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",border:"0"},skipLink:{position:"absolute",top:"-40px",left:"6px",zIndex:"1000",padding:"8px",background:"#000000",color:"#FFFFFF",textDecoration:"none",borderRadius:"4px"}},readability:{dyslexic:{fontFamily:["OpenDyslexic","Comic Sans MS","sans-serif"],fontSize:"1.125rem",lineHeight:"1.6",letterSpacing:"0.05em",wordSpacing:"0.1em"},adhd:{fontSize:"1rem",lineHeight:"1.5",maxWidth:"65ch",marginBottom:"1rem"},lowVision:{fontSize:"1.25rem",lineHeight:"1.6",fontWeight:"500",contrast:"high"}},colorBlind:{patterns:{dots:"radial-gradient(circle, currentColor 1px, transparent 1px)",stripes:"repeating-linear-gradient(45deg, transparent, transparent 2px, currentColor 2px, currentColor 4px)",grid:"linear-gradient(currentColor 1px, transparent 1px), linear-gradient(90deg, currentColor 1px, transparent 1px)"},protanopia:{primary:"#0066CC",secondary:"#FF6600",success:"#0099CC",warning:"#FF9900",error:"#CC0000"},deuteranopia:{primary:"#0066CC",secondary:"#FF6600",success:"#0099CC",warning:"#FF9900",error:"#CC0000"},tritanopia:{primary:"#CC0066",secondary:"#00CC66",success:"#0066CC",warning:"#CC6600",error:"#CC0000"}},spacing:{touchSpacing:"8px",comfortable:{xs:"4px",sm:"8px",md:"16px",lg:"24px",xl:"32px"}},animation:{respectsMotionPreference:!0,safe:{fadeIn:"opacity 200ms ease-in",slideIn:"transform 200ms ease-out",scaleIn:"transform 150ms ease-out"},caution:{spin:"transform 1s linear infinite",bounce:"transform 0.5s ease-in-out infinite alternate",flash:"opacity 0.5s ease-in-out infinite alternate"}}}),c),d),g=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"--cove";const n={},r=function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";Object.keys(e).forEach((o=>{const i=e[o],l=a?"".concat(a,"-").concat(o):o;"object"!==typeof i||null===i||Array.isArray(i)?n["".concat(t,"-").concat(l)]=i:r(i,l)}))};return r(e),n},h=(e,t)=>{const n=e=>{const t=m(e),[n,r,a]=t.map((e=>(e/=255)<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4)));return.2126*n+.7152*r+.0722*a},r=n(e),a=n(t);return(Math.max(r,a)+.05)/(Math.min(r,a)+.05)},m=e=>{const t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return t?[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]:null},v=(e,t)=>t.split(".").reduce(((e,t)=>null===e||void 0===e?void 0:e[t]),e);var y=n(579);const b=(0,t.createContext)(null),x={light:"light",dark:"dark",highContrast:"high-contrast",auto:"auto"},w={default:"default",gulf:"gulf",arabic:"arabic"},S={reducedMotion:"reduce",highContrast:"high",largeText:"large",dyslexicFont:"dyslexic"},k=()=>{const e=(0,t.useContext)(b);if(!e)throw new Error("useTheme must be used within a ThemeProvider");return e},C=()=>{const{a11yPreferences:e,toggleA11yPreference:t,setA11yPreference:n,isReducedMotion:r,isHighContrast:a}=k();return{preferences:e,toggle:t,set:n,isReducedMotion:r(),isHighContrast:a(),announceToScreenReader:e=>{const t=document.createElement("div");t.setAttribute("aria-live","polite"),t.setAttribute("aria-atomic","true"),t.className="sr-only",t.textContent=e,document.body.appendChild(t),setTimeout((()=>{document.body.removeChild(t)}),1e3)},trapFocus:e=>{const t=e.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'),n=t[0],r=t[t.length-1],a=e=>{"Tab"===e.key&&(e.shiftKey?document.activeElement===n&&(r.focus(),e.preventDefault()):document.activeElement===r&&(n.focus(),e.preventDefault()))};return e.addEventListener("keydown",a),()=>{e.removeEventListener("keydown",a)}}}},E=()=>{const{culture:e,language:t,getCulturalVariant:n,getFontFamily:r,isRTL:a}=k();return{culture:e,language:t,isRTL:a(),getCulturalVariant:n,getFontFamily:r,formatNumber:function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t;return new Intl.NumberFormat(n).format(e)},formatDate:function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t;return new Intl.DateTimeFormat(n).format(e)},formatCurrency:function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"AED",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t;return new Intl.NumberFormat(r,{style:"currency",currency:n}).format(e)},getTextDirection:()=>a()?"rtl":"ltr",getLogicalProperty:(e,t)=>u.getLogicalSpacing(e,t,a()?"rtl":"ltr")}},T=e=>{let{children:n,defaultTheme:r=x.auto,defaultCulture:a=w.default,defaultLanguage:o="en"}=e;const[i,l]=(0,t.useState)(r),[c,d]=(0,t.useState)(a),[m,k]=(0,t.useState)(o),[C,E]=(0,t.useState)({reducedMotion:!1,highContrast:!1,largeText:!1,dyslexicFont:!1}),[T,D]=(0,t.useState)({colorScheme:"light",reducedMotion:!1,highContrast:!1});(0,t.useEffect)((()=>{(()=>{const e=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light",t=window.matchMedia("(prefers-reduced-motion: reduce)").matches,n=window.matchMedia("(prefers-contrast: high)").matches;D({colorScheme:e,reducedMotion:t,highContrast:n}),E((e=>s(s({},e),{},{reducedMotion:t,highContrast:n})))})();const e=window.matchMedia("(prefers-color-scheme: dark)"),t=window.matchMedia("(prefers-reduced-motion: reduce)"),n=window.matchMedia("(prefers-contrast: high)"),r=e=>{D((t=>s(s({},t),{},{colorScheme:e.matches?"dark":"light"})))},a=e=>{D((t=>s(s({},t),{},{reducedMotion:e.matches}))),E((t=>s(s({},t),{},{reducedMotion:e.matches})))},o=e=>{D((t=>s(s({},t),{},{highContrast:e.matches}))),E((t=>s(s({},t),{},{highContrast:e.matches})))};return e.addEventListener("change",r),t.addEventListener("change",a),n.addEventListener("change",o),()=>{e.removeEventListener("change",r),t.removeEventListener("change",a),n.removeEventListener("change",o)}}),[]),(0,t.useEffect)((()=>{const e=i===x.auto?T.colorScheme:i;document.documentElement.setAttribute("data-theme",e),C.highContrast&&document.documentElement.setAttribute("data-theme","high-contrast");const t=u.isRTL(m);document.documentElement.setAttribute("dir",t?"rtl":"ltr"),document.documentElement.setAttribute("lang",m),document.documentElement.setAttribute("data-culture",c);const n=[];C.reducedMotion&&n.push("reduced-motion"),C.largeText&&n.push("large-text"),C.dyslexicFont&&n.push("dyslexic-font"),document.body.className=n.join(" ")}),[i,c,m,C,T.colorScheme]);const O=(0,t.useCallback)((e=>{l(e),localStorage.setItem("cove-theme",e)}),[]),j=(0,t.useCallback)((e=>{d(e),localStorage.setItem("cove-culture",e)}),[]),N=(0,t.useCallback)((e=>{k(e),localStorage.setItem("cove-language",e)}),[]),I=(0,t.useCallback)((e=>{E((t=>{const n=s(s({},t),{},{[e]:!t[e]});return localStorage.setItem("cove-a11y-preferences",JSON.stringify(n)),n}))}),[]),_=(0,t.useCallback)(((e,t)=>{E((n=>{const r=s(s({},n),{},{[e]:t});return localStorage.setItem("cove-a11y-preferences",JSON.stringify(r)),r}))}),[]);(0,t.useEffect)((()=>{const e=localStorage.getItem("cove-theme"),t=localStorage.getItem("cove-culture"),n=localStorage.getItem("cove-language"),r=localStorage.getItem("cove-a11y-preferences");if(e&&Object.values(x).includes(e)&&l(e),t&&Object.values(w).includes(t)&&d(t),n&&k(n),r)try{const e=JSON.parse(r);E((t=>s(s({},t),e)))}catch(a){console.warn("Failed to parse saved accessibility preferences:",a)}}),[]);const P=(e=>({tokens:e,cssVariables:g(e),getContrastRatio:(e,t)=>h(e,t),isRTL:()=>"rtl"===document.dir||"rtl"===document.documentElement.dir,getCulturalVariant:function(t){var n;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"default";const a=null===(n=e.cultural)||void 0===n?void 0:n[r];return(null===a||void 0===a?void 0:a[t])||v(e,t)}}))(p),z=i===x.auto?T.colorScheme:i,F=(0,t.useCallback)(((e,t)=>P.getContrastRatio(e,t)),[P]),L=(0,t.useCallback)((e=>P.getCulturalVariant(e,c)),[P,c]),R=(0,t.useCallback)((function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"modern";return u.getFontFamily(m,e)}),[m]),M=(0,t.useCallback)((e=>f.getAnimation(e,C.reducedMotion?"reduced":"default")),[C.reducedMotion]),A={theme:z,culture:c,language:m,a11yPreferences:C,systemPreferences:T,switchTheme:O,switchCulture:j,switchLanguage:N,toggleA11yPreference:I,setA11yPreference:_,getContrastRatio:F,getCulturalVariant:L,getFontFamily:R,getMotionPreference:M,tokens:p,cssVariables:g(p),isRTL:()=>u.isRTL(m),isDarkMode:()=>"dark"===z,isHighContrast:()=>C.highContrast,isReducedMotion:()=>C.reducedMotion,THEMES:x,CULTURES:w,A11Y_PREFERENCES:S};return(0,y.jsx)(b.Provider,{value:A,children:n})};function D(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}if("undefined"!==typeof document){const e=document.createElement("style");e.textContent='\n  @keyframes spin {\n    from {\n      transform: rotate(0deg);\n    }\n    to {\n      transform: rotate(360deg);\n    }\n  }\n  \n  @keyframes dash {\n    0% {\n      stroke-dasharray: 1, 150;\n      stroke-dashoffset: 0;\n    }\n    50% {\n      stroke-dasharray: 90, 150;\n      stroke-dashoffset: -35;\n    }\n    100% {\n      stroke-dasharray: 90, 150;\n      stroke-dashoffset: -124;\n    }\n  }\n  \n  /* Button component styles */\n  .button {\n    position: relative;\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    font-family: var(--cove-font-family-base);\n    font-weight: var(--cove-font-weight-medium);\n    text-align: center;\n    text-decoration: none;\n    border: 1px solid transparent;\n    border-radius: var(--cove-border-radius-button);\n    cursor: pointer;\n    user-select: none;\n    vertical-align: middle;\n    white-space: nowrap;\n    transition: all var(--cove-motion-duration-normal) var(--cove-motion-easing-slide-spring);\n    min-height: var(--cove-a11y-touch-target-minimum);\n    min-width: var(--cove-a11y-touch-target-minimum);\n    outline: none;\n  }\n  \n  .button:disabled,\n  .button[aria-disabled="true"] {\n    opacity: 0.6;\n    cursor: not-allowed;\n    pointer-events: none;\n  }\n  \n  .button:focus-visible {\n    outline: var(--cove-a11y-focus-ring-width) solid var(--cove-a11y-focus-ring-color);\n    outline-offset: var(--cove-a11y-focus-ring-offset);\n    z-index: 1;\n  }\n  \n  .button-text--loading {\n    opacity: 0;\n  }\n  \n  .button-icon {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    flex-shrink: 0;\n  }\n  \n  .button-icon--start {\n    margin-inline-end: var(--cove-spacing-2);\n  }\n  \n  .button-icon--end {\n    margin-inline-start: var(--cove-spacing-2);\n  }\n  \n  .button-spinner {\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n    display: flex;\n    align-items: center;\n    justify-content: center;\n  }\n  \n  .button-spinner-svg {\n    width: 1em;\n    height: 1em;\n    animation: spin 1s linear infinite;\n  }\n  \n  .button-spinner-circle {\n    animation: dash 1.5s ease-in-out infinite;\n  }\n  \n  /* Respect reduced motion preference */\n  @media (prefers-reduced-motion: reduce) {\n    .button {\n      transition: none;\n    }\n    \n    .button:hover:not(:disabled) {\n      transform: none;\n    }\n    \n    .button:active,\n    .button[data-pressed="true"] {\n      transform: none;\n    }\n    \n    .button-spinner-svg {\n      animation: none;\n    }\n    \n    .button-spinner-circle {\n      animation: none;\n    }\n  }\n  \n  /* High contrast mode support */\n  @media (prefers-contrast: high) {\n    .button {\n      border-width: 2px;\n    }\n    \n    .button:focus-visible {\n      outline-width: 3px;\n    }\n  }\n',document.head.appendChild(e)}const O=["children","as","className"],j=e=>{let{children:t,as:n="span",className:r=""}=e,a=D(e,O);return(0,y.jsx)(n,s(s({className:"visually-hidden ".concat(r),style:{position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",border:"0"}},a),{},{children:t}))},N=j,I=["children","variant","size","disabled","loading","loadingText","icon","iconPosition","fullWidth","type","ariaLabel","ariaDescribedBy","ariaExpanded","ariaControls","ariaPressed","onClick","onFocus","onBlur","onKeyDown","className","testId"],_={primary:"primary",secondary:"secondary",tertiary:"tertiary",danger:"danger",success:"success",warning:"warning"},P={sm:"sm",md:"md",lg:"lg",xl:"xl"},z=(0,t.forwardRef)(((e,n)=>{let{children:r,variant:a=_.primary,size:o=P.md,disabled:i=!1,loading:l=!1,loadingText:c="Loading...",icon:u,iconPosition:d="start",fullWidth:f=!1,type:p="button",ariaLabel:g,ariaDescribedBy:h,ariaExpanded:m,ariaControls:v,ariaPressed:b,onClick:x,onFocus:w,onBlur:S,onKeyDown:T,className:O="",testId:N}=e,z=D(e,I);const{tokens:F,isReducedMotion:L,getContrastRatio:R}=k(),{announceToScreenReader:M}=C(),{isRTL:A}=E(),[H,U]=(0,t.useState)(!1),[B,W]=(0,t.useState)(!1),V=(e=>{var t,n,r,a,o,i,l,c,u,d,f,p,g;let{variant:h,size:m,disabled:v,loading:y,fullWidth:b,isPressed:x,isFocused:w,isRTL:S,isReducedMotion:k,tokens:C}=e;const E={position:"relative",display:"inline-flex",alignItems:"center",justifyContent:"center",fontFamily:C.typography.fontFamily.base.join(", "),fontWeight:C.typography.fontWeight.medium,textAlign:"center",textDecoration:"none",border:"1px solid transparent",borderRadius:C.borderRadius.button,cursor:v||y?"not-allowed":"pointer",userSelect:"none",verticalAlign:"middle",whiteSpace:"nowrap",transition:k?"none":"all 200ms cubic-bezier(0.4, 0, 0.2, 1)",minHeight:(null===(t=C.accessibility)||void 0===t||null===(n=t.touchTarget)||void 0===n?void 0:n.minimum)||"44px",minWidth:(null===(r=C.accessibility)||void 0===r||null===(a=r.touchTarget)||void 0===a?void 0:a.minimum)||"44px",width:b?"100%":"auto",outline:"none",opacity:v?.6:1,pointerEvents:v||y?"none":"auto"},T={sm:{fontSize:C.typography.fontSize.sm,lineHeight:C.typography.lineHeight.tight,padding:"".concat(C.spacing[2]," ").concat(C.spacing[3]),gap:C.spacing[1]},md:{fontSize:C.typography.fontSize.base,lineHeight:C.typography.lineHeight.normal,padding:"".concat(C.spacing[3]," ").concat(C.spacing[4]),gap:C.spacing[2]},lg:{fontSize:C.typography.fontSize.lg,lineHeight:C.typography.lineHeight.normal,padding:"".concat(C.spacing[4]," ").concat(C.spacing[6]),gap:C.spacing[2]},xl:{fontSize:C.typography.fontSize.xl,lineHeight:C.typography.lineHeight.normal,padding:"".concat(C.spacing[5]," ").concat(C.spacing[8]),gap:C.spacing[3]}},D={primary:{backgroundColor:C.colors.primary.DEFAULT,color:C.colors.text.inverse,borderColor:C.colors.primary.DEFAULT,"&:hover:not(:disabled)":{backgroundColor:C.colors.primary[700],borderColor:C.colors.primary[700],transform:k?"none":"translateY(-1px)",boxShadow:k?"none":C.boxShadow.md},'&:active, &[data-pressed="true"]':{backgroundColor:C.colors.primary[800],borderColor:C.colors.primary[800],transform:k?"none":"translateY(0)",boxShadow:k?"none":C.boxShadow.sm}},secondary:{backgroundColor:"transparent",color:C.colors.primary.DEFAULT,borderColor:C.colors.primary.DEFAULT,"&:hover:not(:disabled)":{backgroundColor:C.colors.primary[50],transform:k?"none":"translateY(-1px)",boxShadow:k?"none":C.boxShadow.md},'&:active, &[data-pressed="true"]':{backgroundColor:C.colors.primary[100],transform:k?"none":"translateY(0)",boxShadow:k?"none":C.boxShadow.sm}},tertiary:{backgroundColor:"transparent",color:C.colors.text.primary,borderColor:"transparent","&:hover:not(:disabled)":{backgroundColor:C.colors.background.elevated,transform:k?"none":"translateY(-1px)"},'&:active, &[data-pressed="true"]':{backgroundColor:C.colors.border.light,transform:k?"none":"translateY(0)"}},danger:{backgroundColor:C.colors.danger.DEFAULT,color:C.colors.text.inverse,borderColor:C.colors.danger.DEFAULT,"&:hover:not(:disabled)":{backgroundColor:C.colors.danger[700],borderColor:C.colors.danger[700],transform:k?"none":"translateY(-1px)",boxShadow:k?"none":C.boxShadow.md},'&:active, &[data-pressed="true"]':{backgroundColor:C.colors.danger[800],borderColor:C.colors.danger[800],transform:k?"none":"translateY(0)",boxShadow:k?"none":C.boxShadow.sm}},success:{backgroundColor:C.colors.success.DEFAULT,color:C.colors.text.inverse,borderColor:C.colors.success.DEFAULT,"&:hover:not(:disabled)":{backgroundColor:C.colors.success[700],borderColor:C.colors.success[700],transform:k?"none":"translateY(-1px)",boxShadow:k?"none":C.boxShadow.md},'&:active, &[data-pressed="true"]':{backgroundColor:C.colors.success[800],borderColor:C.colors.success[800],transform:k?"none":"translateY(0)",boxShadow:k?"none":C.boxShadow.sm}},warning:{backgroundColor:C.colors.warning.DEFAULT,color:C.colors.text.primary,borderColor:C.colors.warning.DEFAULT,"&:hover:not(:disabled)":{backgroundColor:C.colors.warning[600],borderColor:C.colors.warning[600],transform:k?"none":"translateY(-1px)",boxShadow:k?"none":C.boxShadow.md},'&:active, &[data-pressed="true"]':{backgroundColor:C.colors.warning[700],borderColor:C.colors.warning[700],transform:k?"none":"translateY(0)",boxShadow:k?"none":C.boxShadow.sm}}},O=w?{outline:"".concat((null===(o=C.accessibility)||void 0===o||null===(i=o.focus)||void 0===i||null===(l=i.ring)||void 0===l?void 0:l.width)||"2px"," solid ").concat((null===(c=C.accessibility)||void 0===c||null===(u=c.focus)||void 0===u||null===(d=u.ring)||void 0===d?void 0:d.color)||C.colors.border.focus),outlineOffset:(null===(f=C.accessibility)||void 0===f||null===(p=f.focus)||void 0===p||null===(g=p.ring)||void 0===g?void 0:g.offset)||"2px",zIndex:1}:{},j=x?{transform:k?"none":"scale(0.98)",transition:k?"none":"transform 150ms ease-out"}:{},N=y?{color:"transparent",pointerEvents:"none"}:{},I=S?{direction:"rtl"}:{};return s(s(s(s(s(s(s({},E),T[m]),D[h]),O),j),N),I)})({variant:a,size:o,disabled:i,loading:l,fullWidth:f,isPressed:H,isFocused:B,isRTL:A,isReducedMotion:L,tokens:F}),$=u&&(0,y.jsx)("span",{className:"button-icon ".concat("end"===d?"button-icon--end":"button-icon--start"),"aria-hidden":"true",children:u}),Y=l&&(0,y.jsx)("span",{className:"button-spinner","aria-hidden":"true",role:"status",children:(0,y.jsx)("svg",{className:"button-spinner-svg",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,y.jsx)("circle",{className:"button-spinner-circle",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeDasharray:"31.416",strokeDashoffset:"31.416"})})}),Q={"aria-label":g,"aria-describedby":h,"aria-expanded":m,"aria-controls":v,"aria-pressed":b,"aria-disabled":i||l,"aria-busy":l,"data-testid":N,"data-variant":a,"data-size":o,"data-loading":l,"data-disabled":i};return Object.keys(Q).forEach((e=>{void 0===Q[e]&&delete Q[e]})),(0,y.jsxs)("button",s(s(s({ref:n,type:p,className:"button ".concat(O),style:V,disabled:i||l,onClick:e=>{i||l?e.preventDefault():(g&&!r&&M("".concat(g," activated")),null===x||void 0===x||x(e))},onFocus:e=>{W(!0),null===w||void 0===w||w(e)},onBlur:e=>{W(!1),null===S||void 0===S||S(e)},onKeyDown:e=>{" "!==e.key&&"Enter"!==e.key||i||l||(U(!0),setTimeout((()=>U(!1)),150)),null===T||void 0===T||T(e)},onMouseDown:()=>{i||l||U(!0)},onMouseUp:()=>{U(!1)},onMouseLeave:()=>{U(!1)}},Q),z),{},{children:[l&&(0,y.jsxs)(y.Fragment,{children:[Y,(0,y.jsx)(j,{children:c})]}),!l&&"start"===d&&$,(0,y.jsx)("span",{className:"button-text ".concat(l?"button-text--loading":""),"aria-hidden":l,children:r}),!l&&"end"===d&&$,!(()=>{var e;const t=(null===(e=F.colors[a])||void 0===e?void 0:e.DEFAULT)||F.colors.primary.DEFAULT,n="tertiary"===a?F.colors.text.primary:F.colors.text.inverse;return R(t,n)>=4.5})()&&(0,y.jsx)(j,{children:"Warning: This button may have insufficient color contrast"})]}))}));z.displayName="Button",z.variants=_,z.sizes=P;const F=z,L=["children","variant","weight","size","color","align","as","truncate","maxLines","fontFamily","dyslexicFriendly","arabicOptimized","className","style"],R={h1:"h1",h2:"h2",h3:"h3",h4:"h4",h5:"h5",h6:"h6",body:"body",bodyLarge:"bodyLarge",bodySmall:"bodySmall",caption:"caption",overline:"overline"},M=(0,t.forwardRef)(((e,t)=>{let{children:n,variant:r=R.body,weight:a,size:o,color:i,align:l,as:c,truncate:u=!1,maxLines:d,fontFamily:f,dyslexicFriendly:p=!1,arabicOptimized:g=!1,className:h="",style:m={}}=e,v=D(e,L);const{tokens:b,a11yPreferences:x}=k(),{language:w,isRTL:S,getFontFamily:C}=E(),T=(()=>{const e={h1:{fontSize:b.typography.fontSize["5xl"],fontWeight:b.typography.fontWeight.bold,lineHeight:b.typography.lineHeight.tight,letterSpacing:b.typography.letterSpacing.tight},h2:{fontSize:b.typography.fontSize["4xl"],fontWeight:b.typography.fontWeight.bold,lineHeight:b.typography.lineHeight.tight,letterSpacing:b.typography.letterSpacing.tight},h3:{fontSize:b.typography.fontSize["3xl"],fontWeight:b.typography.fontWeight.semibold,lineHeight:b.typography.lineHeight.snug,letterSpacing:b.typography.letterSpacing.normal},h4:{fontSize:b.typography.fontSize["2xl"],fontWeight:b.typography.fontWeight.semibold,lineHeight:b.typography.lineHeight.snug,letterSpacing:b.typography.letterSpacing.normal},h5:{fontSize:b.typography.fontSize.xl,fontWeight:b.typography.fontWeight.medium,lineHeight:b.typography.lineHeight.normal,letterSpacing:b.typography.letterSpacing.normal},h6:{fontSize:b.typography.fontSize.lg,fontWeight:b.typography.fontWeight.medium,lineHeight:b.typography.lineHeight.normal,letterSpacing:b.typography.letterSpacing.normal},body:{fontSize:b.typography.fontSize.base,fontWeight:b.typography.fontWeight.normal,lineHeight:b.typography.lineHeight.relaxed,letterSpacing:b.typography.letterSpacing.normal},bodyLarge:{fontSize:b.typography.fontSize.lg,fontWeight:b.typography.fontWeight.normal,lineHeight:b.typography.lineHeight.relaxed,letterSpacing:b.typography.letterSpacing.normal},bodySmall:{fontSize:b.typography.fontSize.sm,fontWeight:b.typography.fontWeight.normal,lineHeight:b.typography.lineHeight.normal,letterSpacing:b.typography.letterSpacing.normal},caption:{fontSize:b.typography.fontSize.xs,fontWeight:b.typography.fontWeight.normal,lineHeight:b.typography.lineHeight.normal,letterSpacing:b.typography.letterSpacing.wide},overline:{fontSize:b.typography.fontSize.xs,fontWeight:b.typography.fontWeight.medium,lineHeight:b.typography.lineHeight.normal,letterSpacing:b.typography.letterSpacing.widest,textTransform:"uppercase"}};return e[r]||e.body})(),O=s(s(s(s(s(s(s({fontFamily:f||(p||x.dyslexicFont?b.typography.fontFamily.dyslexic.join(", "):g||S?C("modern"):b.typography.fontFamily.base.join(", ")),fontSize:o||T.fontSize,fontWeight:a?b.typography.fontWeight[a]:T.fontWeight,lineHeight:T.lineHeight,letterSpacing:T.letterSpacing,color:(()=>{if(!i)return b.colors.text.primary;if("string"===typeof i&&i.includes(".")){return i.split(".").reduce(((e,t)=>null===e||void 0===e?void 0:e[t]),b.colors)||i}return i})(),textAlign:(()=>{if(l){if(S)switch(l){case"start":return"right";case"end":return"left";default:return l}switch(l){case"start":return"left";case"end":return"right";default:return l}}})(),direction:S?"rtl":"ltr",margin:0},u&&{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}),d&&{display:"-webkit-box",WebkitLineClamp:d,WebkitBoxOrient:"vertical",overflow:"hidden"}),(g||S)&&{fontFeatureSettings:'"calt" 1, "liga" 1, "kern" 1',textRendering:"optimizeLegibility"}),(p||x.dyslexicFont)&&{letterSpacing:"0.05em",wordSpacing:"0.1em",lineHeight:1.6}),x.largeText&&{fontSize:"calc(".concat(T.fontSize," * 1.25)"),lineHeight:1.6,maxWidth:"65ch"}),T),m),j=(()=>{if(c)return c;switch(r){case R.h1:case R.h2:case R.h3:case R.h4:case R.h5:case R.h6:return r;default:return"p"}})();return(0,y.jsx)(j,s(s({ref:t,className:"text text--".concat(r," ").concat(h),style:O,dir:S?"rtl":"ltr",lang:w},v),{},{children:n}))}));M.displayName="Text",M.variants=R,M.weights={light:"light",normal:"normal",medium:"medium",semibold:"semibold",bold:"bold"},M.aligns={start:"start",center:"center",end:"end",justify:"justify"};const A=M,H=["children","size","padding","paddingX","paddingY","paddingTop","paddingBottom","paddingStart","paddingEnd","margin","marginX","marginY","marginTop","marginBottom","marginStart","marginEnd","centerContent","fluid","arabicOptimized","className","style"],U={xs:"xs",sm:"sm",md:"md",lg:"lg",xl:"xl","2xl":"2xl",full:"full",arabicMobile:"arabicMobile",arabicTablet:"arabicTablet",arabicDesktop:"arabicDesktop"},B={none:"none",xs:"xs",sm:"sm",md:"md",lg:"lg",xl:"xl"},W=(0,t.forwardRef)(((e,t)=>{let{children:n,size:r=U.lg,padding:a=B.md,paddingX:o,paddingY:i,paddingTop:l,paddingBottom:c,paddingStart:u,paddingEnd:d,margin:f=B.none,marginX:p,marginY:g,marginTop:h,marginBottom:m,marginStart:v,marginEnd:b,centerContent:x=!1,fluid:w=!1,arabicOptimized:S=!1,className:C="",style:T={}}=e,O=D(e,H);const{tokens:j}=k(),{isRTL:N}=E(),I=e=>{if(!e||"none"===e)return"0";const t={xs:j.spacing[2],sm:j.spacing[4],md:j.spacing[6],lg:j.spacing[8],xl:j.spacing[12]};return t[e]||t.md},_=e=>{const t="margin"===e,n=t?"margin":"padding",r=t?f:a,s=t?p:o,y=t?g:i,x=t?h:l,w=t?m:c,S=t?v:u,k=t?b:d,C={};if(r&&"none"!==r){const e=I(r);C[n]=e}if(s){const e=I(s);C["".concat(n,"-inline")]=e}if(y){const e=I(y);C["".concat(n,"-block")]=e}return x&&(C["".concat(n,"-block-start")]=I(x)),w&&(C["".concat(n,"-block-end")]=I(w)),S&&(C["".concat(n,"-inline-start")]=I(S)),k&&(C["".concat(n,"-inline-end")]=I(k)),C},P=s(s(s(s(s(s(s({width:"100%",maxWidth:(()=>{if(w)return"100%";const e={xs:"320px",sm:"640px",md:"768px",lg:"1024px",xl:"1280px","2xl":"1536px",full:"100%",arabicMobile:"375px",arabicTablet:"768px",arabicDesktop:"1200px"};return e[r]||e.lg})(),marginInlineStart:x?"auto":void 0,marginInlineEnd:x?"auto":void 0,direction:N?"rtl":"ltr"},_("padding")),_("margin")),(S||N)&&{textAlign:"start",fontFeatureSettings:'"calt" 1, "liga" 1, "kern" 1',textRendering:"optimizeLegibility"}),"arabicMobile"===r&&{"@media (min-width: 640px)":{maxWidth:"640px"}}),"arabicTablet"===r&&{"@media (min-width: 1024px)":{maxWidth:"1024px"}}),"arabicDesktop"===r&&{"@media (min-width: 1280px)":{maxWidth:"1200px"}}),T);return(0,y.jsx)("div",s(s({ref:t,className:"container container--".concat(r," ").concat(S?"container--arabic":""," ").concat(C),style:P,dir:N?"rtl":"ltr"},O),{},{children:n}))}));W.displayName="Container",W.sizes=U,W.spacing=B;const V=W,$=["label","placeholder","helperText","errorMessage","successMessage","warningMessage","variant","size","state","disabled","required","readOnly","type","value","defaultValue","onChange","onFocus","onBlur","onKeyDown","startIcon","endIcon","fullWidth","arabicOptimized","className","style","inputProps","labelProps","helperProps","testId"],Y={default:"default",filled:"filled",outlined:"outlined"},Q={sm:"sm",md:"md",lg:"lg"},q={default:"default",error:"error",warning:"warning",success:"success"},K=(0,t.forwardRef)(((e,n)=>{let{label:r,placeholder:a,helperText:o,errorMessage:i,successMessage:l,warningMessage:c,variant:u=Y.outlined,size:d=Q.md,state:f=q.default,disabled:p=!1,required:g=!1,readOnly:h=!1,type:m="text",value:v,defaultValue:b,onChange:x,onFocus:w,onBlur:S,onKeyDown:T,startIcon:O,endIcon:N,fullWidth:I=!1,arabicOptimized:_=!1,className:P="",style:z={},inputProps:F={},labelProps:L={},helperProps:R={},testId:M}=e,A=D(e,$);const{tokens:H,getContrastRatio:U}=k(),{announceToScreenReader:B}=C(),{isRTL:W}=E(),[V,K]=(0,t.useState)(!1),[X,G]=(0,t.useState)(Boolean(v||b)),Z=(0,t.useId)(),J=(0,t.useId)(),ee=((0,t.useId)(),i?q.error:c?q.warning:l?q.success:f),te=(()=>{const e={default:{border:H.colors.border.medium,borderFocus:H.colors.border.focus,text:H.colors.text.primary},error:{border:H.colors.danger.DEFAULT,borderFocus:H.colors.danger[600],text:H.colors.danger[700]},warning:{border:H.colors.warning.DEFAULT,borderFocus:H.colors.warning[600],text:H.colors.warning[700]},success:{border:H.colors.success.DEFAULT,borderFocus:H.colors.success[600],text:H.colors.success[700]}};return e[ee]||e.default})(),ne=(()=>{const e={sm:{fontSize:H.typography.fontSize.sm,padding:"".concat(H.spacing[2]," ").concat(H.spacing[3]),height:"36px",iconSize:"16px"},md:{fontSize:H.typography.fontSize.base,padding:"".concat(H.spacing[3]," ").concat(H.spacing[4]),height:"44px",iconSize:"20px"},lg:{fontSize:H.typography.fontSize.lg,padding:"".concat(H.spacing[4]," ").concat(H.spacing[5]),height:"52px",iconSize:"24px"}};return e[d]||e.md})(),re=s(s(s(s(s({width:I?"100%":"auto",height:ne.height,fontSize:ne.fontSize,fontFamily:_||W?H.typography.fontFamily.arabic.join(", "):H.typography.fontFamily.base.join(", "),color:p?H.colors.text.disabled:H.colors.text.primary,backgroundColor:p?H.colors.background.elevated:H.colors.background.paper,border:"1px solid ".concat(V?te.borderFocus:te.border),borderRadius:H.borderRadius.input,padding:ne.padding,paddingInlineStart:O?"calc(".concat(ne.padding.split(" ")[1]," + ").concat(ne.iconSize," + ").concat(H.spacing[2],")"):void 0,paddingInlineEnd:N?"calc(".concat(ne.padding.split(" ")[1]," + ").concat(ne.iconSize," + ").concat(H.spacing[2],")"):void 0,outline:"none",transition:"border-color 200ms ease, box-shadow 200ms ease",direction:W?"rtl":"ltr"},V&&{borderColor:te.borderFocus,boxShadow:"0 0 0 3px ".concat(te.borderFocus,"20")}),p&&{cursor:"not-allowed",opacity:.6}),h&&{backgroundColor:H.colors.background.elevated,cursor:"default"}),(_||W)&&{fontFeatureSettings:'"calt" 1, "liga" 1, "kern" 1',textRendering:"optimizeLegibility"}),z),ae={position:"relative",display:"inline-flex",flexDirection:"column",width:I?"100%":"auto"},oe={fontSize:H.typography.fontSize.sm,fontWeight:H.typography.fontWeight.medium,color:p?H.colors.text.disabled:H.colors.text.primary,marginBlockEnd:H.spacing[1],direction:W?"rtl":"ltr"},ie={fontSize:H.typography.fontSize.xs,color:te.text,marginBlockStart:H.spacing[1],direction:W?"rtl":"ltr"},le={position:"absolute",top:"50%",transform:"translateY(-50%)",width:ne.iconSize,height:ne.iconSize,color:p?H.colors.text.disabled:H.colors.text.secondary,pointerEvents:"none",zIndex:1},se=s(s({},le),{},{insetInlineStart:H.spacing[3]}),ce=s(s({},le),{},{insetInlineEnd:H.spacing[3]}),ue=i||c||l||o,de={id:Z,"aria-describedby":ue?J:void 0,"aria-invalid":ee===q.error,"aria-required":g,"data-testid":M};return(0,y.jsxs)("div",{className:"input-container input-container--".concat(u," input-container--").concat(d," ").concat(P),style:ae,children:[r&&(0,y.jsxs)("label",s(s({htmlFor:Z,className:"input-label",style:oe},L),{},{children:[r,g&&(0,y.jsx)("span",{className:"input-required","aria-label":"required",style:{color:H.colors.danger.DEFAULT,marginInlineStart:H.spacing[1]},children:"*"})]})),(0,y.jsxs)("div",{style:{position:"relative",display:"flex",alignItems:"center"},children:[O&&(0,y.jsx)("span",{className:"input-icon input-icon--start",style:se,"aria-hidden":"true",children:O}),(0,y.jsx)("input",s(s(s({ref:n,type:m,placeholder:a,value:v,defaultValue:b,disabled:p,readOnly:h,className:"input-field",style:re,onChange:e=>{const t=e.target.value;G(Boolean(t)),null===x||void 0===x||x(e)},onFocus:e=>{K(!0),null===w||void 0===w||w(e)},onBlur:e=>{K(!1),null===S||void 0===S||S(e)},onKeyDown:e=>{"Enter"===e.key&&(i?B("Error: ".concat(i)):c?B("Warning: ".concat(c)):l&&B("Success: ".concat(l))),null===T||void 0===T||T(e)}},de),F),A)),N&&(0,y.jsx)("span",{className:"input-icon input-icon--end",style:ce,"aria-hidden":"true",children:N})]}),ue&&(0,y.jsx)("div",s(s({id:J,className:"input-helper ".concat(ee!==q.default?"input-helper--".concat(ee):""),style:ie,role:ee===q.error?"alert":void 0,"aria-live":ee===q.error?"polite":void 0},R),{},{children:ue})),ee===q.error&&(0,y.jsx)(j,{children:(0,y.jsxs)("div",{role:"status","aria-live":"polite",children:["Input has an error: ",i]})})]})}));K.displayName="Input",K.variants=Y,K.sizes=Q,K.states=q;const X=K,G=e=>{let{title:t,children:n,id:r}=e;return(0,y.jsxs)("section",{id:r,style:{marginBottom:"3rem"},children:[(0,y.jsx)(A,{variant:"h2",style:{marginBottom:"1.5rem",borderBottom:"2px solid #e5e7eb",paddingBottom:"0.5rem"},children:t}),n]})},Z=()=>{const{theme:e,switchTheme:t,culture:n,switchCulture:r,language:a,switchLanguage:o,THEMES:i,CULTURES:l}=k(),{preferences:s,toggle:c}=C();return(0,y.jsxs)(V,{padding:"lg",style:{backgroundColor:"var(--cove-color-background-elevated)",borderRadius:"8px",marginBottom:"2rem"},children:[(0,y.jsx)(A,{variant:"h3",style:{marginBottom:"1rem"},children:"Theme & Accessibility Controls"}),(0,y.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(250px, 1fr))",gap:"1rem",marginBottom:"1rem"},children:[(0,y.jsxs)("div",{children:[(0,y.jsx)(A,{variant:"bodySmall",weight:"medium",style:{marginBottom:"0.5rem",display:"block"},children:"Theme"}),(0,y.jsx)("div",{style:{display:"flex",gap:"0.5rem",flexWrap:"wrap"},children:Object.values(i).map((n=>(0,y.jsx)(F,{variant:e===n?"primary":"secondary",size:"sm",onClick:()=>t(n),children:n.charAt(0).toUpperCase()+n.slice(1)},n)))})]}),(0,y.jsxs)("div",{children:[(0,y.jsx)(A,{variant:"bodySmall",weight:"medium",style:{marginBottom:"0.5rem",display:"block"},children:"Culture"}),(0,y.jsx)("div",{style:{display:"flex",gap:"0.5rem",flexWrap:"wrap"},children:Object.values(l).map((e=>(0,y.jsx)(F,{variant:n===e?"primary":"secondary",size:"sm",onClick:()=>r(e),children:e.charAt(0).toUpperCase()+e.slice(1)},e)))})]}),(0,y.jsxs)("div",{children:[(0,y.jsx)(A,{variant:"bodySmall",weight:"medium",style:{marginBottom:"0.5rem",display:"block"},children:"Language"}),(0,y.jsxs)("div",{style:{display:"flex",gap:"0.5rem"},children:[(0,y.jsx)(F,{variant:"en"===a?"primary":"secondary",size:"sm",onClick:()=>o("en"),children:"English"}),(0,y.jsx)(F,{variant:"ar"===a?"primary":"secondary",size:"sm",onClick:()=>o("ar"),children:"\u0627\u0644\u0639\u0631\u0628\u064a\u0629"})]})]})]}),(0,y.jsxs)("div",{children:[(0,y.jsx)(A,{variant:"bodySmall",weight:"medium",style:{marginBottom:"0.5rem",display:"block"},children:"Accessibility Preferences"}),(0,y.jsxs)("div",{style:{display:"flex",gap:"0.5rem",flexWrap:"wrap"},children:[(0,y.jsx)(F,{variant:s.reducedMotion?"success":"tertiary",size:"sm",onClick:()=>c("reducedMotion"),children:"Reduced Motion"}),(0,y.jsx)(F,{variant:s.highContrast?"success":"tertiary",size:"sm",onClick:()=>c("highContrast"),children:"High Contrast"}),(0,y.jsx)(F,{variant:s.dyslexicFont?"success":"tertiary",size:"sm",onClick:()=>c("dyslexicFont"),children:"Dyslexic Font"}),(0,y.jsx)(F,{variant:s.largeText?"success":"tertiary",size:"sm",onClick:()=>c("largeText"),children:"Large Text"})]})]})]})},J=()=>{const[e,n]=(0,t.useState)(!1),{language:r}=E();return(0,y.jsx)(G,{title:"ar"===r?"\u0639\u0631\u0636 \u0627\u0644\u0623\u0632\u0631\u0627\u0631":"Button Showcase",id:"buttons",children:(0,y.jsxs)("div",{style:{display:"grid",gap:"1rem"},children:[(0,y.jsxs)("div",{children:[(0,y.jsx)(A,{variant:"h4",style:{marginBottom:"1rem"},children:"ar"===r?"\u0623\u0646\u0648\u0627\u0639 \u0627\u0644\u0623\u0632\u0631\u0627\u0631":"Button Variants"}),(0,y.jsxs)("div",{style:{display:"flex",gap:"1rem",flexWrap:"wrap",alignItems:"center"},children:[(0,y.jsx)(F,{variant:"primary",children:"ar"===r?"\u0623\u0633\u0627\u0633\u064a":"Primary"}),(0,y.jsx)(F,{variant:"secondary",children:"ar"===r?"\u062b\u0627\u0646\u0648\u064a":"Secondary"}),(0,y.jsx)(F,{variant:"tertiary",children:"ar"===r?"\u062b\u0627\u0644\u062b\u064a":"Tertiary"}),(0,y.jsx)(F,{variant:"success",children:"ar"===r?"\u0646\u062c\u062d":"Success"}),(0,y.jsx)(F,{variant:"warning",children:"ar"===r?"\u062a\u062d\u0630\u064a\u0631":"Warning"}),(0,y.jsx)(F,{variant:"danger",children:"ar"===r?"\u062e\u0637\u0631":"Danger"})]})]}),(0,y.jsxs)("div",{children:[(0,y.jsx)(A,{variant:"h4",style:{marginBottom:"1rem"},children:"ar"===r?"\u0623\u062d\u062c\u0627\u0645 \u0627\u0644\u0623\u0632\u0631\u0627\u0631":"Button Sizes"}),(0,y.jsxs)("div",{style:{display:"flex",gap:"1rem",alignItems:"center",flexWrap:"wrap"},children:[(0,y.jsx)(F,{size:"sm",children:"ar"===r?"\u0635\u063a\u064a\u0631":"Small"}),(0,y.jsx)(F,{size:"md",children:"ar"===r?"\u0645\u062a\u0648\u0633\u0637":"Medium"}),(0,y.jsx)(F,{size:"lg",children:"ar"===r?"\u0643\u0628\u064a\u0631":"Large"}),(0,y.jsx)(F,{size:"xl",children:"ar"===r?"\u0643\u0628\u064a\u0631 \u062c\u062f\u0627\u064b":"Extra Large"})]})]}),(0,y.jsxs)("div",{children:[(0,y.jsx)(A,{variant:"h4",style:{marginBottom:"1rem"},children:"ar"===r?"\u062d\u0627\u0644\u0627\u062a \u0627\u0644\u0623\u0632\u0631\u0627\u0631":"Button States"}),(0,y.jsxs)("div",{style:{display:"flex",gap:"1rem",alignItems:"center",flexWrap:"wrap"},children:[(0,y.jsx)(F,{children:"ar"===r?"\u0639\u0627\u062f\u064a":"Normal"}),(0,y.jsx)(F,{disabled:!0,children:"ar"===r?"\u0645\u0639\u0637\u0644":"Disabled"}),(0,y.jsx)(F,{loading:e,loadingText:"ar"===r?"\u062c\u0627\u0631\u064a \u0627\u0644\u062a\u062d\u0645\u064a\u0644...":"Loading...",onClick:()=>{n(!0),setTimeout((()=>n(!1)),3e3)},children:"ar"===r?"\u062a\u062d\u0645\u064a\u0644":"Loading Demo"})]})]})]})})},ee=()=>{const{language:e}=E(),{preferences:t}=C(),n="The quick brown fox jumps over the lazy dog",r="\u0646\u0635 \u062a\u062c\u0631\u064a\u0628\u064a \u0628\u0627\u0644\u0644\u063a\u0629 \u0627\u0644\u0639\u0631\u0628\u064a\u0629 \u0644\u0639\u0631\u0636 \u0627\u0644\u062e\u0637\u0648\u0637 \u0648\u0627\u0644\u062a\u0646\u0633\u064a\u0642";return(0,y.jsx)(G,{title:"ar"===e?"\u0639\u0631\u0636 \u0627\u0644\u0637\u0628\u0627\u0639\u0629":"Typography Showcase",id:"typography",children:(0,y.jsxs)("div",{style:{display:"grid",gap:"2rem"},children:[(0,y.jsxs)("div",{children:[(0,y.jsx)(A,{variant:"h4",style:{marginBottom:"1rem"},children:"ar"===e?"\u0627\u0644\u0639\u0646\u0627\u0648\u064a\u0646":"Headings"}),(0,y.jsxs)("div",{style:{display:"grid",gap:"0.5rem"},children:[(0,y.jsx)(A,{variant:"h1",arabicOptimized:"ar"===e,children:"ar"===e?"\u0639\u0646\u0648\u0627\u0646 \u0631\u0626\u064a\u0633\u064a (H1)":"Main Heading (H1)"}),(0,y.jsx)(A,{variant:"h2",arabicOptimized:"ar"===e,children:"ar"===e?"\u0639\u0646\u0648\u0627\u0646 \u0641\u0631\u0639\u064a (H2)":"Sub Heading (H2)"}),(0,y.jsx)(A,{variant:"h3",arabicOptimized:"ar"===e,children:"ar"===e?"\u0639\u0646\u0648\u0627\u0646 \u062b\u0627\u0644\u062b\u064a (H3)":"Third Heading (H3)"}),(0,y.jsx)(A,{variant:"h4",arabicOptimized:"ar"===e,children:"ar"===e?"\u0639\u0646\u0648\u0627\u0646 \u0631\u0627\u0628\u0639 (H4)":"Fourth Heading (H4)"}),(0,y.jsx)(A,{variant:"h5",arabicOptimized:"ar"===e,children:"ar"===e?"\u0639\u0646\u0648\u0627\u0646 \u062e\u0627\u0645\u0633 (H5)":"Fifth Heading (H5)"}),(0,y.jsx)(A,{variant:"h6",arabicOptimized:"ar"===e,children:"ar"===e?"\u0639\u0646\u0648\u0627\u0646 \u0633\u0627\u062f\u0633 (H6)":"Sixth Heading (H6)"})]})]}),(0,y.jsxs)("div",{children:[(0,y.jsx)(A,{variant:"h4",style:{marginBottom:"1rem"},children:"ar"===e?"\u0646\u0635 \u0627\u0644\u0645\u062d\u062a\u0648\u0649":"Body Text"}),(0,y.jsxs)("div",{style:{display:"grid",gap:"1rem"},children:[(0,y.jsx)(A,{variant:"bodyLarge",arabicOptimized:"ar"===e,children:"ar"===e?"\u0646\u0635 \u0643\u0628\u064a\u0631: "+r:"Large body text: "+n}),(0,y.jsx)(A,{variant:"body",arabicOptimized:"ar"===e,children:"ar"===e?"\u0646\u0635 \u0639\u0627\u062f\u064a: "+r:"Regular body text: "+n}),(0,y.jsx)(A,{variant:"bodySmall",arabicOptimized:"ar"===e,children:"ar"===e?"\u0646\u0635 \u0635\u063a\u064a\u0631: "+r:"Small body text: "+n}),(0,y.jsx)(A,{variant:"caption",arabicOptimized:"ar"===e,children:"ar"===e?"\u062a\u0633\u0645\u064a\u0629 \u062a\u0648\u0636\u064a\u062d\u064a\u0629: "+r:"Caption text: "+n})]})]}),(0,y.jsxs)("div",{children:[(0,y.jsx)(A,{variant:"h4",style:{marginBottom:"1rem"},children:"ar"===e?"\u0645\u064a\u0632\u0627\u062a \u0625\u0645\u0643\u0627\u0646\u064a\u0629 \u0627\u0644\u0648\u0635\u0648\u0644":"Accessibility Features"}),(0,y.jsxs)("div",{style:{display:"grid",gap:"1rem"},children:[(0,y.jsx)(A,{variant:"body",dyslexicFriendly:t.dyslexicFont,children:"ar"===e?"\u0646\u0635 \u0645\u0646\u0627\u0633\u0628 \u0644\u0639\u0633\u0631 \u0627\u0644\u0642\u0631\u0627\u0621\u0629 (\u0639\u0646\u062f \u0627\u0644\u062a\u0641\u0639\u064a\u0644): "+r:"Dyslexia-friendly text (when enabled): "+n}),(0,y.jsx)(A,{variant:"body",arabicOptimized:"ar"===e,style:{fontSize:t.largeText?"1.25em":void 0},children:"ar"===e?"\u0646\u0635 \u0643\u0628\u064a\u0631 (\u0639\u0646\u062f \u0627\u0644\u062a\u0641\u0639\u064a\u0644): "+r:"Large text (when enabled): "+n})]})]})]})})},te=()=>{const{language:e}=E(),[n,r]=(0,t.useState)({name:"",email:"",message:""}),[a,o]=(0,t.useState)({});return(0,y.jsx)(G,{title:"ar"===e?"\u0639\u0631\u0636 \u0627\u0644\u0646\u0645\u0627\u0630\u062c":"Form Showcase",id:"forms",children:(0,y.jsx)(V,{size:"md",children:(0,y.jsxs)("form",{onSubmit:t=>{t.preventDefault();const r={};n.name||(r.name="ar"===e?"\u0627\u0644\u0627\u0633\u0645 \u0645\u0637\u0644\u0648\u0628":"Name is required"),n.email||(r.email="ar"===e?"\u0627\u0644\u0628\u0631\u064a\u062f \u0627\u0644\u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a \u0645\u0637\u0644\u0648\u0628":"Email is required"),o(r),0===Object.keys(r).length&&alert("ar"===e?"\u062a\u0645 \u0625\u0631\u0633\u0627\u0644 \u0627\u0644\u0646\u0645\u0648\u0630\u062c \u0628\u0646\u062c\u0627\u062d!":"Form submitted successfully!")},style:{display:"grid",gap:"1.5rem"},children:[(0,y.jsx)(X,{label:"ar"===e?"\u0627\u0644\u0627\u0633\u0645 \u0627\u0644\u0643\u0627\u0645\u0644":"Full Name",placeholder:"ar"===e?"\u0623\u062f\u062e\u0644 \u0627\u0633\u0645\u0643 \u0627\u0644\u0643\u0627\u0645\u0644":"Enter your full name",value:n.name,onChange:e=>r(s(s({},n),{},{name:e.target.value})),errorMessage:a.name,required:!0,arabicOptimized:"ar"===e,fullWidth:!0}),(0,y.jsx)(X,{type:"email",label:"ar"===e?"\u0627\u0644\u0628\u0631\u064a\u062f \u0627\u0644\u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a":"Email Address",placeholder:"ar"===e?"\u0623\u062f\u062e\u0644 \u0628\u0631\u064a\u062f\u0643 \u0627\u0644\u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a":"Enter your email address",value:n.email,onChange:e=>r(s(s({},n),{},{email:e.target.value})),errorMessage:a.email,helperText:"ar"===e?"\u0644\u0646 \u0646\u0634\u0627\u0631\u0643 \u0628\u0631\u064a\u062f\u0643 \u0645\u0639 \u0623\u062d\u062f":"We'll never share your email",required:!0,arabicOptimized:"ar"===e,fullWidth:!0}),(0,y.jsx)(X,{label:"ar"===e?"\u0627\u0644\u0631\u0633\u0627\u0644\u0629":"Message",placeholder:"ar"===e?"\u0627\u0643\u062a\u0628 \u0631\u0633\u0627\u0644\u062a\u0643 \u0647\u0646\u0627":"Write your message here",value:n.message,onChange:e=>r(s(s({},n),{},{message:e.target.value})),arabicOptimized:"ar"===e,fullWidth:!0}),(0,y.jsxs)("div",{style:{display:"flex",gap:"1rem",justifyContent:"ar"===e?"flex-start":"flex-end"},children:[(0,y.jsx)(F,{type:"button",variant:"secondary",children:"ar"===e?"\u0625\u0644\u063a\u0627\u0621":"Cancel"}),(0,y.jsx)(F,{type:"submit",variant:"primary",children:"ar"===e?"\u0625\u0631\u0633\u0627\u0644":"Submit"})]})]})})})},ne=()=>{const{language:e,isRTL:t}=E();return(0,y.jsx)(G,{title:"ar"===e?"\u0639\u0631\u0636 \u0627\u0644\u062a\u062e\u0637\u064a\u0637":"Layout Showcase",id:"layout",children:(0,y.jsxs)("div",{style:{display:"grid",gap:"2rem"},children:[(0,y.jsxs)("div",{children:[(0,y.jsx)(A,{variant:"h4",style:{marginBottom:"1rem"},children:"ar"===e?"\u0623\u062d\u062c\u0627\u0645 \u0627\u0644\u062d\u0627\u0648\u064a\u0627\u062a":"Container Sizes"}),(0,y.jsx)("div",{style:{display:"grid",gap:"1rem"},children:["xs","sm","md","lg","xl"].map((t=>(0,y.jsx)(V,{size:t,padding:"md",style:{backgroundColor:"var(--cove-color-background-elevated)",border:"1px solid var(--cove-color-border-light)",borderRadius:"4px"},children:(0,y.jsxs)(A,{variant:"body",children:["ar"===e?"\u062d\u0627\u0648\u064a\u0629 ".concat(t):"Container ".concat(t)," -","ar"===e?" \u0647\u0630\u0627 \u0646\u0635 \u062a\u062c\u0631\u064a\u0628\u064a \u0644\u0639\u0631\u0636 \u062d\u062c\u0645 \u0627\u0644\u062d\u0627\u0648\u064a\u0629":" This is sample text to show container size"]})},t)))})]}),(0,y.jsxs)("div",{children:[(0,y.jsx)(A,{variant:"h4",style:{marginBottom:"1rem"},children:"ar"===e?"\u0639\u0631\u0636 \u0627\u0644\u062a\u062e\u0637\u064a\u0637 \u0645\u0646 \u0627\u0644\u064a\u0645\u064a\u0646 \u0644\u0644\u064a\u0633\u0627\u0631":"RTL Layout Demo"}),(0,y.jsxs)(V,{arabicOptimized:t,padding:"lg",style:{backgroundColor:"var(--cove-color-background-elevated)",border:"1px solid var(--cove-color-border-light)",borderRadius:"8px"},children:[(0,y.jsxs)("div",{style:{display:"flex",gap:"1rem",alignItems:"center",marginBottom:"1rem"},children:[(0,y.jsxs)(F,{size:"sm",children:["ar"===e?"\u0632\u0631":"Button"," 1"]}),(0,y.jsxs)(F,{size:"sm",variant:"secondary",children:["ar"===e?"\u0632\u0631":"Button"," 2"]}),(0,y.jsxs)(F,{size:"sm",variant:"tertiary",children:["ar"===e?"\u0632\u0631":"Button"," 3"]})]}),(0,y.jsx)(A,{variant:"body",arabicOptimized:t,children:"ar"===e?"\u0647\u0630\u0627 \u0646\u0635 \u062a\u062c\u0631\u064a\u0628\u064a \u0644\u0639\u0631\u0636 \u0643\u064a\u0641\u064a\u0629 \u0639\u0645\u0644 \u0627\u0644\u062a\u062e\u0637\u064a\u0637 \u0645\u0646 \u0627\u0644\u064a\u0645\u064a\u0646 \u0625\u0644\u0649 \u0627\u0644\u064a\u0633\u0627\u0631. \u064a\u062c\u0628 \u0623\u0646 \u064a\u0638\u0647\u0631 \u0627\u0644\u0646\u0635 \u0648\u0627\u0644\u0639\u0646\u0627\u0635\u0631 \u0628\u0634\u0643\u0644 \u0635\u062d\u064a\u062d \u0641\u064a \u0627\u0644\u0627\u062a\u062c\u0627\u0647 \u0627\u0644\u0645\u0646\u0627\u0633\u0628.":"This is sample text to demonstrate RTL layout functionality. Text and elements should appear correctly oriented for the current language direction."})]})]})]})})},re=()=>{const{language:e}=E();return(0,y.jsxs)("div",{style:{minHeight:"100vh",backgroundColor:"var(--cove-color-background-light)"},children:[(0,y.jsx)("a",{href:"#main-content",className:"skip-link",style:{position:"absolute",top:"-40px",left:"6px",zIndex:1e3,padding:"8px",background:"#000",color:"#fff",textDecoration:"none",borderRadius:"4px"},children:"ar"===e?"\u062a\u062e\u0637\u064a \u0625\u0644\u0649 \u0627\u0644\u0645\u062d\u062a\u0648\u0649 \u0627\u0644\u0631\u0626\u064a\u0633\u064a":"Skip to main content"}),(0,y.jsxs)(V,{size:"xl",padding:"lg",children:[(0,y.jsxs)("header",{style:{textAlign:"center",marginBottom:"3rem"},children:[(0,y.jsx)(A,{variant:"h1",style:{marginBottom:"1rem"},children:"ar"===e?"\u0646\u0638\u0627\u0645 \u062a\u0635\u0645\u064a\u0645 \u0643\u0648\u0641":"Cove Design System"}),(0,y.jsx)(A,{variant:"bodyLarge",color:"text.secondary",children:"ar"===e?"\u0627\u0644\u0645\u0631\u062d\u0644\u0629 1.1 - \u0646\u0638\u0627\u0645 \u0627\u0644\u062a\u0635\u0645\u064a\u0645 \u0648\u0623\u0633\u0627\u0633 \u0625\u0645\u0643\u0627\u0646\u064a\u0629 \u0627\u0644\u0648\u0635\u0648\u0644":"Phase 1.1 - Design System & Accessibility Foundation"}),(0,y.jsx)(A,{variant:"body",style:{marginTop:"1rem"},children:"ar"===e?"\u0646\u0638\u0627\u0645 \u062a\u0635\u0645\u064a\u0645 \u0634\u0627\u0645\u0644 \u0645\u0639 \u062f\u0639\u0645 \u0643\u0627\u0645\u0644 \u0644\u0625\u0645\u0643\u0627\u0646\u064a\u0629 \u0627\u0644\u0648\u0635\u0648\u0644 \u0648\u0627\u0644\u062a\u062e\u0637\u064a\u0637 \u0645\u0646 \u0627\u0644\u064a\u0645\u064a\u0646 \u0644\u0644\u064a\u0633\u0627\u0631 \u0648\u0627\u0644\u062a\u0643\u064a\u0641 \u0627\u0644\u062b\u0642\u0627\u0641\u064a":"Comprehensive design system with full accessibility support, RTL layouts, and cultural adaptation"})]}),(0,y.jsx)(Z,{}),(0,y.jsxs)("main",{id:"main-content",children:[(0,y.jsx)(J,{}),(0,y.jsx)(ee,{}),(0,y.jsx)(te,{}),(0,y.jsx)(ne,{})]}),(0,y.jsx)("footer",{style:{marginTop:"4rem",paddingTop:"2rem",borderTop:"1px solid var(--cove-color-border-light)",textAlign:"center"},children:(0,y.jsx)(A,{variant:"caption",color:"text.secondary",children:"ar"===e?"\u0646\u0638\u0627\u0645 \u062a\u0635\u0645\u064a\u0645 \u0643\u0648\u0641 - \u0645\u062a\u0648\u0627\u0641\u0642 \u0645\u0639 WCAG 2.1 AA - \u062f\u0639\u0645 \u0643\u0627\u0645\u0644 \u0644\u0644\u063a\u0629 \u0627\u0644\u0639\u0631\u0628\u064a\u0629 \u0648\u0627\u0644\u062a\u062e\u0637\u064a\u0637 \u0645\u0646 \u0627\u0644\u064a\u0645\u064a\u0646 \u0644\u0644\u064a\u0633\u0627\u0631":"Cove Design System - WCAG 2.1 AA Compliant - Full Arabic & RTL Support"})})]}),(0,y.jsx)(N,{children:(0,y.jsx)("div",{role:"status","aria-live":"polite",id:"announcements"})})]})},ae=()=>(0,y.jsx)(T,{defaultTheme:"auto",defaultLanguage:"en",children:(0,y.jsx)(re,{})});function oe(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var ie="function"===typeof Symbol&&Symbol.observable||"@@observable",le=function(){return Math.random().toString(36).substring(7).split("").join(".")},se={INIT:"@@redux/INIT"+le(),REPLACE:"@@redux/REPLACE"+le(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+le()}};function ce(e){if("object"!==typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function ue(e,t,n){var r;if("function"===typeof t&&"function"===typeof n||"function"===typeof n&&"function"===typeof arguments[3])throw new Error(oe(0));if("function"===typeof t&&"undefined"===typeof n&&(n=t,t=void 0),"undefined"!==typeof n){if("function"!==typeof n)throw new Error(oe(1));return n(ue)(e,t)}if("function"!==typeof e)throw new Error(oe(2));var a=e,o=t,i=[],l=i,s=!1;function c(){l===i&&(l=i.slice())}function u(){if(s)throw new Error(oe(3));return o}function d(e){if("function"!==typeof e)throw new Error(oe(4));if(s)throw new Error(oe(5));var t=!0;return c(),l.push(e),function(){if(t){if(s)throw new Error(oe(6));t=!1,c();var n=l.indexOf(e);l.splice(n,1),i=null}}}function f(e){if(!ce(e))throw new Error(oe(7));if("undefined"===typeof e.type)throw new Error(oe(8));if(s)throw new Error(oe(9));try{s=!0,o=a(o,e)}finally{s=!1}for(var t=i=l,n=0;n<t.length;n++){(0,t[n])()}return e}return f({type:se.INIT}),(r={dispatch:f,subscribe:d,getState:u,replaceReducer:function(e){if("function"!==typeof e)throw new Error(oe(10));a=e,f({type:se.REPLACE})}})[ie]=function(){var e,t=d;return(e={subscribe:function(e){if("object"!==typeof e||null===e)throw new Error(oe(11));function n(){e.next&&e.next(u())}return n(),{unsubscribe:t(n)}}})[ie]=function(){return this},e},r}function de(e,t){for(var n=arguments.length,r=new Array(n>2?n-2:0),a=2;a<n;a++)r[a-2]=arguments[a];if("undefined"!==typeof process&&void 0===t)throw new Error("invariant requires an error message argument");if(!e){let e;if(void 0===t)e=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{let n=0;e=new Error(t.replace(/%s/g,(function(){return r[n++]}))),e.name="Invariant Violation"}throw e.framesToPop=1,e}}function fe(e){return"object"===typeof e}const pe="dnd-core/INIT_COORDS",ge="dnd-core/BEGIN_DRAG",he="dnd-core/PUBLISH_DRAG_SOURCE",me="dnd-core/HOVER",ve="dnd-core/DROP",ye="dnd-core/END_DRAG";function be(e,t){return{type:pe,payload:{sourceClientOffset:t||null,clientOffset:e||null}}}const xe={type:pe,payload:{clientOffset:null,sourceClientOffset:null}};function we(e){return function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{publishSource:!0};const{publishSource:r=!0,clientOffset:a,getSourceClientOffset:o}=n,i=e.getMonitor(),l=e.getRegistry();e.dispatch(be(a)),function(e,t,n){de(!t.isDragging(),"Cannot call beginDrag while dragging."),e.forEach((function(e){de(n.getSource(e),"Expected sourceIds to be registered.")}))}(t,i,l);const s=function(e,t){let n=null;for(let r=e.length-1;r>=0;r--)if(t.canDragSource(e[r])){n=e[r];break}return n}(t,i);if(null==s)return void e.dispatch(xe);let c=null;if(a){if(!o)throw new Error("getSourceClientOffset must be defined");!function(e){de("function"===typeof e,"When clientOffset is provided, getSourceClientOffset must be a function.")}(o),c=o(s)}e.dispatch(be(a,c));const u=l.getSource(s).beginDrag(i,s);if(null==u)return;!function(e){de(fe(e),"Item must be an object.")}(u),l.pinSource(s);const d=l.getSourceType(s);return{type:ge,payload:{itemType:d,item:u,sourceId:s,clientOffset:a||null,sourceClientOffset:c||null,isSourcePublic:!!r}}}}function Se(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ke(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Se(e,t,n[t])}))}return e}function Ce(e){return function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const n=e.getMonitor(),r=e.getRegistry();!function(e){de(e.isDragging(),"Cannot call drop while not dragging."),de(!e.didDrop(),"Cannot call drop twice during one drag operation.")}(n);const a=function(e){const t=e.getTargetIds().filter(e.canDropOnTarget,e);return t.reverse(),t}(n);a.forEach(((a,o)=>{const i=function(e,t,n,r){const a=n.getTarget(e);let o=a?a.drop(r,e):void 0;(function(e){de("undefined"===typeof e||fe(e),"Drop result must either be an object or undefined.")})(o),"undefined"===typeof o&&(o=0===t?{}:r.getDropResult());return o}(a,o,r,n),l={type:ve,payload:{dropResult:ke({},t,i)}};e.dispatch(l)}))}}function Ee(e){return function(){const t=e.getMonitor(),n=e.getRegistry();!function(e){de(e.isDragging(),"Cannot call endDrag while not dragging.")}(t);const r=t.getSourceId();if(null!=r){n.getSource(r,!0).endDrag(t,r),n.unpinSource()}return{type:ye}}}function Te(e,t){return null===t?null===e:Array.isArray(e)?e.some((e=>e===t)):e===t}function De(e){return function(t){let{clientOffset:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};!function(e){de(Array.isArray(e),"Expected targetIds to be an array.")}(t);const r=t.slice(0),a=e.getMonitor(),o=e.getRegistry();return function(e,t,n){for(let r=e.length-1;r>=0;r--){const a=e[r];Te(t.getTargetType(a),n)||e.splice(r,1)}}(r,o,a.getItemType()),function(e,t,n){de(t.isDragging(),"Cannot call hover while not dragging."),de(!t.didDrop(),"Cannot call hover after drop.");for(let r=0;r<e.length;r++){const t=e[r];de(e.lastIndexOf(t)===r,"Expected targetIds to be unique in the passed array.");de(n.getTarget(t),"Expected targetIds to be registered.")}}(r,a,o),function(e,t,n){e.forEach((function(e){n.getTarget(e).hover(t,e)}))}(r,a,o),{type:me,payload:{targetIds:r,clientOffset:n||null}}}}function Oe(e){return function(){if(e.getMonitor().isDragging())return{type:he}}}class je{receiveBackend(e){this.backend=e}getMonitor(){return this.monitor}getBackend(){return this.backend}getRegistry(){return this.monitor.registry}getActions(){const e=this,{dispatch:t}=this.store;const n=function(e){return{beginDrag:we(e),publishDragSource:Oe(e),hover:De(e),drop:Ce(e),endDrag:Ee(e)}}(this);return Object.keys(n).reduce(((r,a)=>{const o=n[a];var i;return r[a]=(i=o,function(){for(var n=arguments.length,r=new Array(n),a=0;a<n;a++)r[a]=arguments[a];const o=i.apply(e,r);"undefined"!==typeof o&&t(o)}),r}),{})}dispatch(e){this.store.dispatch(e)}constructor(e,t){this.isSetUp=!1,this.handleRefCountChange=()=>{const e=this.store.getState().refCount>0;this.backend&&(e&&!this.isSetUp?(this.backend.setup(),this.isSetUp=!0):!e&&this.isSetUp&&(this.backend.teardown(),this.isSetUp=!1))},this.store=e,this.monitor=t,e.subscribe(this.handleRefCountChange)}}function Ne(e,t){return{x:e.x-t.x,y:e.y-t.y}}const Ie=[],_e=[];Ie.__IS_NONE__=!0,_e.__IS_ALL__=!0;class Pe{subscribeToStateChange(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{handlerIds:n}=t;de("function"===typeof e,"listener must be a function."),de("undefined"===typeof n||Array.isArray(n),"handlerIds, when specified, must be an array of strings.");let r=this.store.getState().stateId;return this.store.subscribe((()=>{const t=this.store.getState(),a=t.stateId;try{const o=a===r||a===r+1&&!function(e,t){if(e===Ie)return!1;if(e===_e||"undefined"===typeof t)return!0;const n=(r=e,t.filter((e=>r.indexOf(e)>-1)));var r;return n.length>0}(t.dirtyHandlerIds,n);o||e()}finally{r=a}}))}subscribeToOffsetChange(e){de("function"===typeof e,"listener must be a function.");let t=this.store.getState().dragOffset;return this.store.subscribe((()=>{const n=this.store.getState().dragOffset;n!==t&&(t=n,e())}))}canDragSource(e){if(!e)return!1;const t=this.registry.getSource(e);return de(t,"Expected to find a valid source. sourceId=".concat(e)),!this.isDragging()&&t.canDrag(this,e)}canDropOnTarget(e){if(!e)return!1;const t=this.registry.getTarget(e);if(de(t,"Expected to find a valid target. targetId=".concat(e)),!this.isDragging()||this.didDrop())return!1;return Te(this.registry.getTargetType(e),this.getItemType())&&t.canDrop(this,e)}isDragging(){return Boolean(this.getItemType())}isDraggingSource(e){if(!e)return!1;const t=this.registry.getSource(e,!0);if(de(t,"Expected to find a valid source. sourceId=".concat(e)),!this.isDragging()||!this.isSourcePublic())return!1;return this.registry.getSourceType(e)===this.getItemType()&&t.isDragging(this,e)}isOverTarget(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{shallow:!1};if(!e)return!1;const{shallow:n}=t;if(!this.isDragging())return!1;const r=this.registry.getTargetType(e),a=this.getItemType();if(a&&!Te(r,a))return!1;const o=this.getTargetIds();if(!o.length)return!1;const i=o.indexOf(e);return n?i===o.length-1:i>-1}getItemType(){return this.store.getState().dragOperation.itemType}getItem(){return this.store.getState().dragOperation.item}getSourceId(){return this.store.getState().dragOperation.sourceId}getTargetIds(){return this.store.getState().dragOperation.targetIds}getDropResult(){return this.store.getState().dragOperation.dropResult}didDrop(){return this.store.getState().dragOperation.didDrop}isSourcePublic(){return Boolean(this.store.getState().dragOperation.isSourcePublic)}getInitialClientOffset(){return this.store.getState().dragOffset.initialClientOffset}getInitialSourceClientOffset(){return this.store.getState().dragOffset.initialSourceClientOffset}getClientOffset(){return this.store.getState().dragOffset.clientOffset}getSourceClientOffset(){return function(e){const{clientOffset:t,initialClientOffset:n,initialSourceClientOffset:r}=e;return t&&n&&r?Ne(function(e,t){return{x:e.x+t.x,y:e.y+t.y}}(t,r),n):null}(this.store.getState().dragOffset)}getDifferenceFromInitialOffset(){return function(e){const{clientOffset:t,initialClientOffset:n}=e;return t&&n?Ne(t,n):null}(this.store.getState().dragOffset)}constructor(e,t){this.store=e,this.registry=t}}const ze="undefined"!==typeof global?global:self,Fe=ze.MutationObserver||ze.WebKitMutationObserver;function Le(e){return function(){const t=setTimeout(r,0),n=setInterval(r,50);function r(){clearTimeout(t),clearInterval(n),e()}}}const Re="function"===typeof Fe?function(e){let t=1;const n=new Fe(e),r=document.createTextNode("");return n.observe(r,{characterData:!0}),function(){t=-t,r.data=t}}:Le;class Me{call(){try{this.task&&this.task()}catch(e){this.onError(e)}finally{this.task=null,this.release(this)}}constructor(e,t){this.onError=e,this.release=t,this.task=null}}const Ae=new class{enqueueTask(e){const{queue:t,requestFlush:n}=this;t.length||(n(),this.flushing=!0),t[t.length]=e}constructor(){this.queue=[],this.pendingErrors=[],this.flushing=!1,this.index=0,this.capacity=1024,this.flush=()=>{const{queue:e}=this;for(;this.index<e.length;){const t=this.index;if(this.index++,e[t].call(),this.index>this.capacity){for(let t=0,n=e.length-this.index;t<n;t++)e[t]=e[t+this.index];e.length-=this.index,this.index=0}}e.length=0,this.index=0,this.flushing=!1},this.registerPendingError=e=>{this.pendingErrors.push(e),this.requestErrorThrow()},this.requestFlush=Re(this.flush),this.requestErrorThrow=Le((()=>{if(this.pendingErrors.length)throw this.pendingErrors.shift()}))}},He=new class{create(e){const t=this.freeTasks,n=t.length?t.pop():new Me(this.onError,(e=>t[t.length]=e));return n.task=e,n}constructor(e){this.onError=e,this.freeTasks=[]}}(Ae.registerPendingError);const Ue="dnd-core/ADD_SOURCE",Be="dnd-core/ADD_TARGET",We="dnd-core/REMOVE_SOURCE",Ve="dnd-core/REMOVE_TARGET";function $e(e,t){t&&Array.isArray(e)?e.forEach((e=>$e(e,!1))):de("string"===typeof e||"symbol"===typeof e,t?"Type can only be a string, a symbol, or an array of either.":"Type can only be a string or a symbol.")}var Ye;!function(e){e.SOURCE="SOURCE",e.TARGET="TARGET"}(Ye||(Ye={}));let Qe=0;function qe(e){const t=(Qe++).toString();switch(e){case Ye.SOURCE:return"S".concat(t);case Ye.TARGET:return"T".concat(t);default:throw new Error("Unknown Handler Role: ".concat(e))}}function Ke(e){switch(e[0]){case"S":return Ye.SOURCE;case"T":return Ye.TARGET;default:throw new Error("Cannot parse handler ID: ".concat(e))}}function Xe(e,t){const n=e.entries();let r=!1;do{const{done:e,value:[,a]}=n.next();if(a===t)return!0;r=!!e}while(!r);return!1}class Ge{addSource(e,t){$e(e),function(e){de("function"===typeof e.canDrag,"Expected canDrag to be a function."),de("function"===typeof e.beginDrag,"Expected beginDrag to be a function."),de("function"===typeof e.endDrag,"Expected endDrag to be a function.")}(t);const n=this.addHandler(Ye.SOURCE,e,t);return this.store.dispatch(function(e){return{type:Ue,payload:{sourceId:e}}}(n)),n}addTarget(e,t){$e(e,!0),function(e){de("function"===typeof e.canDrop,"Expected canDrop to be a function."),de("function"===typeof e.hover,"Expected hover to be a function."),de("function"===typeof e.drop,"Expected beginDrag to be a function.")}(t);const n=this.addHandler(Ye.TARGET,e,t);return this.store.dispatch(function(e){return{type:Be,payload:{targetId:e}}}(n)),n}containsHandler(e){return Xe(this.dragSources,e)||Xe(this.dropTargets,e)}getSource(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];de(this.isSourceId(e),"Expected a valid source ID.");return t&&e===this.pinnedSourceId?this.pinnedSource:this.dragSources.get(e)}getTarget(e){return de(this.isTargetId(e),"Expected a valid target ID."),this.dropTargets.get(e)}getSourceType(e){return de(this.isSourceId(e),"Expected a valid source ID."),this.types.get(e)}getTargetType(e){return de(this.isTargetId(e),"Expected a valid target ID."),this.types.get(e)}isSourceId(e){return Ke(e)===Ye.SOURCE}isTargetId(e){return Ke(e)===Ye.TARGET}removeSource(e){var t;de(this.getSource(e),"Expected an existing source."),this.store.dispatch(function(e){return{type:We,payload:{sourceId:e}}}(e)),t=()=>{this.dragSources.delete(e),this.types.delete(e)},Ae.enqueueTask(He.create(t))}removeTarget(e){de(this.getTarget(e),"Expected an existing target."),this.store.dispatch(function(e){return{type:Ve,payload:{targetId:e}}}(e)),this.dropTargets.delete(e),this.types.delete(e)}pinSource(e){const t=this.getSource(e);de(t,"Expected an existing source."),this.pinnedSourceId=e,this.pinnedSource=t}unpinSource(){de(this.pinnedSource,"No source is pinned at the time."),this.pinnedSourceId=null,this.pinnedSource=null}addHandler(e,t,n){const r=qe(e);return this.types.set(r,t),e===Ye.SOURCE?this.dragSources.set(r,n):e===Ye.TARGET&&this.dropTargets.set(r,n),r}constructor(e){this.types=new Map,this.dragSources=new Map,this.dropTargets=new Map,this.pinnedSourceId=null,this.pinnedSource=null,this.store=e}}const Ze=(e,t)=>e===t;function Je(){let e=arguments.length>1?arguments[1]:void 0;switch(e.type){case me:break;case Ue:case Be:case Ve:case We:return Ie;default:return _e}const{targetIds:t=[],prevTargetIds:n=[]}=e.payload,r=function(e,t){const n=new Map,r=e=>{n.set(e,n.has(e)?n.get(e)+1:1)};e.forEach(r),t.forEach(r);const a=[];return n.forEach(((e,t)=>{1===e&&a.push(t)})),a}(t,n),a=r.length>0||!function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Ze;if(e.length!==t.length)return!1;for(let r=0;r<e.length;++r)if(!n(e[r],t[r]))return!1;return!0}(t,n);if(!a)return Ie;const o=n[n.length-1],i=t[t.length-1];return o!==i&&(o&&r.push(o),i&&r.push(i)),r}function et(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const tt={initialSourceClientOffset:null,initialClientOffset:null,clientOffset:null};function nt(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:tt,t=arguments.length>1?arguments[1]:void 0;const{payload:n}=t;switch(t.type){case pe:case ge:return{initialSourceClientOffset:n.sourceClientOffset,initialClientOffset:n.clientOffset,clientOffset:n.clientOffset};case me:return r=e.clientOffset,a=n.clientOffset,!r&&!a||r&&a&&r.x===a.x&&r.y===a.y?e:function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){et(e,t,n[t])}))}return e}({},e,{clientOffset:n.clientOffset});case ye:case ve:return tt;default:return e}var r,a}function rt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function at(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){rt(e,t,n[t])}))}return e}const ot={itemType:null,item:null,sourceId:null,targetIds:[],dropResult:null,didDrop:!1,isSourcePublic:null};function it(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ot,t=arguments.length>1?arguments[1]:void 0;const{payload:n}=t;switch(t.type){case ge:return at({},e,{itemType:n.itemType,item:n.item,sourceId:n.sourceId,isSourcePublic:n.isSourcePublic,dropResult:null,didDrop:!1});case he:return at({},e,{isSourcePublic:!0});case me:return at({},e,{targetIds:n.targetIds});case Ve:return-1===e.targetIds.indexOf(n.targetId)?e:at({},e,{targetIds:(r=e.targetIds,a=n.targetId,r.filter((e=>e!==a)))});case ve:return at({},e,{dropResult:n.dropResult,didDrop:!0,targetIds:[]});case ye:return at({},e,{itemType:null,item:null,sourceId:null,dropResult:null,didDrop:!1,isSourcePublic:null,targetIds:[]});default:return e}var r,a}function lt(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;switch((arguments.length>1?arguments[1]:void 0).type){case Ue:case Be:return e+1;case We:case Ve:return e-1;default:return e}}function st(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:0)+1}function ct(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ut(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){ct(e,t,n[t])}))}return e}function dt(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return{dirtyHandlerIds:Je(e.dirtyHandlerIds,{type:t.type,payload:ut({},t.payload,{prevTargetIds:(n=e,r="dragOperation.targetIds",a=[],r.split(".").reduce(((e,t)=>e&&e[t]?e[t]:a||null),n))})}),dragOffset:nt(e.dragOffset,t),refCount:lt(e.refCount,t),dragOperation:it(e.dragOperation,t),stateId:st(e.stateId)};var n,r,a}function ft(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const r=function(e){const t="undefined"!==typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__;return ue(dt,e&&t&&t({name:"dnd-core",instanceId:"dnd-core"}))}(arguments.length>3&&void 0!==arguments[3]&&arguments[3]),a=new Pe(r,new Ge(r)),o=new je(r,a),i=e(o,t,n);return o.receiveBackend(i),o}const pt=(0,t.createContext)({dragDropManager:void 0});function gt(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}let ht=0;const mt=Symbol.for("__REACT_DND_CONTEXT_INSTANCE__");var vt=(0,t.memo)((function(e){var{children:n}=e,r=gt(e,["children"]);const[a,o]=function(e){if("manager"in e){return[{dragDropManager:e.manager},!1]}const t=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:yt(),n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0;const a=t;a[mt]||(a[mt]={dragDropManager:ft(e,t,n,r)});return a[mt]}(e.backend,e.context,e.options,e.debugMode),n=!e.context;return[t,n]}(r);return(0,t.useEffect)((()=>{if(o){const e=yt();return++ht,()=>{0===--ht&&(e[mt]=null)}}}),[]),(0,y.jsx)(pt.Provider,{value:a,children:n})}));function yt(){return"undefined"!==typeof global?global:window}function bt(e){let t=null;return()=>(null==t&&(t=e()),t)}class xt{enter(e){const t=this.entered.length;return this.entered=function(e,t){const n=new Set,r=e=>n.add(e);e.forEach(r),t.forEach(r);const a=[];return n.forEach((e=>a.push(e))),a}(this.entered.filter((t=>this.isNodeInDocument(t)&&(!t.contains||t.contains(e)))),[e]),0===t&&this.entered.length>0}leave(e){const t=this.entered.length;var n,r;return this.entered=(n=this.entered.filter(this.isNodeInDocument),r=e,n.filter((e=>e!==r))),t>0&&0===this.entered.length}reset(){this.entered=[]}constructor(e){this.entered=[],this.isNodeInDocument=e}}class wt{initializeExposedProperties(){Object.keys(this.config.exposeProperties).forEach((e=>{Object.defineProperty(this.item,e,{configurable:!0,enumerable:!0,get:()=>(console.warn("Browser doesn't allow reading \"".concat(e,'" until the drop event.')),null)})}))}loadDataTransfer(e){if(e){const t={};Object.keys(this.config.exposeProperties).forEach((n=>{const r=this.config.exposeProperties[n];null!=r&&(t[n]={value:r(e,this.config.matchesTypes),configurable:!0,enumerable:!0})})),Object.defineProperties(this.item,t)}}canDrag(){return!0}beginDrag(){return this.item}isDragging(e,t){return t===e.getSourceId()}endDrag(){}constructor(e){this.config=e,this.item={},this.initializeExposedProperties()}}const St="__NATIVE_FILE__",kt="__NATIVE_URL__",Ct="__NATIVE_TEXT__",Et="__NATIVE_HTML__";function Tt(e,t,n){const r=t.reduce(((t,n)=>t||e.getData(n)),"");return null!=r?r:n}const Dt={[St]:{exposeProperties:{files:e=>Array.prototype.slice.call(e.files),items:e=>e.items,dataTransfer:e=>e},matchesTypes:["Files"]},[Et]:{exposeProperties:{html:(e,t)=>Tt(e,t,""),dataTransfer:e=>e},matchesTypes:["Html","text/html"]},[kt]:{exposeProperties:{urls:(e,t)=>Tt(e,t,"").split("\n"),dataTransfer:e=>e},matchesTypes:["Url","text/uri-list"]},[Ct]:{exposeProperties:{text:(e,t)=>Tt(e,t,""),dataTransfer:e=>e},matchesTypes:["Text","text/plain"]}};function Ot(e){if(!e)return null;const t=Array.prototype.slice.call(e.types||[]);return Object.keys(Dt).filter((e=>{const n=Dt[e];return!!(null===n||void 0===n?void 0:n.matchesTypes)&&n.matchesTypes.some((e=>t.indexOf(e)>-1))}))[0]||null}const jt=bt((()=>/firefox/i.test(navigator.userAgent))),Nt=bt((()=>Boolean(window.safari)));class It{interpolate(e){const{xs:t,ys:n,c1s:r,c2s:a,c3s:o}=this;let i=t.length-1;if(e===t[i])return n[i];let l,s=0,c=o.length-1;for(;s<=c;){l=Math.floor(.5*(s+c));const r=t[l];if(r<e)s=l+1;else{if(!(r>e))return n[l];c=l-1}}i=Math.max(0,c);const u=e-t[i],d=u*u;return n[i]+r[i]*u+a[i]*d+o[i]*u*d}constructor(e,t){const{length:n}=e,r=[];for(let p=0;p<n;p++)r.push(p);r.sort(((t,n)=>e[t]<e[n]?-1:1));const a=[],o=[],i=[];let l,s;for(let p=0;p<n-1;p++)l=e[p+1]-e[p],s=t[p+1]-t[p],o.push(l),a.push(s),i.push(s/l);const c=[i[0]];for(let p=0;p<o.length-1;p++){const e=i[p],t=i[p+1];if(e*t<=0)c.push(0);else{l=o[p];const n=o[p+1],r=l+n;c.push(3*r/((r+n)/e+(r+l)/t))}}c.push(i[i.length-1]);const u=[],d=[];let f;for(let p=0;p<c.length-1;p++){f=i[p];const e=c[p],t=1/o[p],n=e+c[p+1]-f-f;u.push((f-e-n)*t),d.push(n*t*t)}this.xs=e,this.ys=t,this.c1s=c,this.c2s=u,this.c3s=d}}function _t(e){const t=1===e.nodeType?e:e.parentElement;if(!t)return null;const{top:n,left:r}=t.getBoundingClientRect();return{x:r,y:n}}function Pt(e){return{x:e.clientX,y:e.clientY}}function zt(e,t,n,r,a){const o=function(e){var t;return"IMG"===e.nodeName&&(jt()||!(null===(t=document.documentElement)||void 0===t?void 0:t.contains(e)))}(t),i=_t(o?e:t),l={x:n.x-i.x,y:n.y-i.y},{offsetWidth:s,offsetHeight:c}=e,{anchorX:u,anchorY:d}=r,{dragPreviewWidth:f,dragPreviewHeight:p}=function(e,t,n,r){let a=e?t.width:n,o=e?t.height:r;return Nt()&&e&&(o/=window.devicePixelRatio,a/=window.devicePixelRatio),{dragPreviewWidth:a,dragPreviewHeight:o}}(o,t,s,c),{offsetX:g,offsetY:h}=a,m=0===h||h;return{x:0===g||g?g:new It([0,.5,1],[l.x,l.x/s*f,l.x+f-s]).interpolate(u),y:m?h:(()=>{let e=new It([0,.5,1],[l.y,l.y/c*p,l.y+p-c]).interpolate(d);return Nt()&&o&&(e+=(window.devicePixelRatio-1)*p),e})()}}class Ft{get window(){return this.globalContext?this.globalContext:"undefined"!==typeof window?window:void 0}get document(){var e;return(null===(e=this.globalContext)||void 0===e?void 0:e.document)?this.globalContext.document:this.window?this.window.document:void 0}get rootElement(){var e;return(null===(e=this.optionsArgs)||void 0===e?void 0:e.rootElement)||this.window}constructor(e,t){this.ownerDocument=null,this.globalContext=e,this.optionsArgs=t}}function Lt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Rt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Lt(e,t,n[t])}))}return e}class Mt{profile(){var e,t;return{sourcePreviewNodes:this.sourcePreviewNodes.size,sourcePreviewNodeOptions:this.sourcePreviewNodeOptions.size,sourceNodeOptions:this.sourceNodeOptions.size,sourceNodes:this.sourceNodes.size,dragStartSourceIds:(null===(e=this.dragStartSourceIds)||void 0===e?void 0:e.length)||0,dropTargetIds:this.dropTargetIds.length,dragEnterTargetIds:this.dragEnterTargetIds.length,dragOverTargetIds:(null===(t=this.dragOverTargetIds)||void 0===t?void 0:t.length)||0}}get window(){return this.options.window}get document(){return this.options.document}get rootElement(){return this.options.rootElement}setup(){const e=this.rootElement;if(void 0!==e){if(e.__isReactDndBackendSetUp)throw new Error("Cannot have two HTML5 backends at the same time.");e.__isReactDndBackendSetUp=!0,this.addEventListeners(e)}}teardown(){const e=this.rootElement;var t;void 0!==e&&(e.__isReactDndBackendSetUp=!1,this.removeEventListeners(this.rootElement),this.clearCurrentDragSourceNode(),this.asyncEndDragFrameId&&(null===(t=this.window)||void 0===t||t.cancelAnimationFrame(this.asyncEndDragFrameId)))}connectDragPreview(e,t,n){return this.sourcePreviewNodeOptions.set(e,n),this.sourcePreviewNodes.set(e,t),()=>{this.sourcePreviewNodes.delete(e),this.sourcePreviewNodeOptions.delete(e)}}connectDragSource(e,t,n){this.sourceNodes.set(e,t),this.sourceNodeOptions.set(e,n);const r=t=>this.handleDragStart(t,e),a=e=>this.handleSelectStart(e);return t.setAttribute("draggable","true"),t.addEventListener("dragstart",r),t.addEventListener("selectstart",a),()=>{this.sourceNodes.delete(e),this.sourceNodeOptions.delete(e),t.removeEventListener("dragstart",r),t.removeEventListener("selectstart",a),t.setAttribute("draggable","false")}}connectDropTarget(e,t){const n=t=>this.handleDragEnter(t,e),r=t=>this.handleDragOver(t,e),a=t=>this.handleDrop(t,e);return t.addEventListener("dragenter",n),t.addEventListener("dragover",r),t.addEventListener("drop",a),()=>{t.removeEventListener("dragenter",n),t.removeEventListener("dragover",r),t.removeEventListener("drop",a)}}addEventListeners(e){e.addEventListener&&(e.addEventListener("dragstart",this.handleTopDragStart),e.addEventListener("dragstart",this.handleTopDragStartCapture,!0),e.addEventListener("dragend",this.handleTopDragEndCapture,!0),e.addEventListener("dragenter",this.handleTopDragEnter),e.addEventListener("dragenter",this.handleTopDragEnterCapture,!0),e.addEventListener("dragleave",this.handleTopDragLeaveCapture,!0),e.addEventListener("dragover",this.handleTopDragOver),e.addEventListener("dragover",this.handleTopDragOverCapture,!0),e.addEventListener("drop",this.handleTopDrop),e.addEventListener("drop",this.handleTopDropCapture,!0))}removeEventListeners(e){e.removeEventListener&&(e.removeEventListener("dragstart",this.handleTopDragStart),e.removeEventListener("dragstart",this.handleTopDragStartCapture,!0),e.removeEventListener("dragend",this.handleTopDragEndCapture,!0),e.removeEventListener("dragenter",this.handleTopDragEnter),e.removeEventListener("dragenter",this.handleTopDragEnterCapture,!0),e.removeEventListener("dragleave",this.handleTopDragLeaveCapture,!0),e.removeEventListener("dragover",this.handleTopDragOver),e.removeEventListener("dragover",this.handleTopDragOverCapture,!0),e.removeEventListener("drop",this.handleTopDrop),e.removeEventListener("drop",this.handleTopDropCapture,!0))}getCurrentSourceNodeOptions(){const e=this.monitor.getSourceId(),t=this.sourceNodeOptions.get(e);return Rt({dropEffect:this.altKeyPressed?"copy":"move"},t||{})}getCurrentDropEffect(){return this.isDraggingNativeItem()?"copy":this.getCurrentSourceNodeOptions().dropEffect}getCurrentSourcePreviewNodeOptions(){const e=this.monitor.getSourceId();return Rt({anchorX:.5,anchorY:.5,captureDraggingState:!1},this.sourcePreviewNodeOptions.get(e)||{})}isDraggingNativeItem(){const t=this.monitor.getItemType();return Object.keys(e).some((n=>e[n]===t))}beginDragNativeItem(e,t){this.clearCurrentDragSourceNode(),this.currentNativeSource=function(e,t){const n=Dt[e];if(!n)throw new Error("native type ".concat(e," has no configuration"));const r=new wt(n);return r.loadDataTransfer(t),r}(e,t),this.currentNativeHandle=this.registry.addSource(e,this.currentNativeSource),this.actions.beginDrag([this.currentNativeHandle])}setCurrentDragSourceNode(e){this.clearCurrentDragSourceNode(),this.currentDragSourceNode=e;this.mouseMoveTimeoutTimer=setTimeout((()=>{var e;return null===(e=this.rootElement)||void 0===e?void 0:e.addEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)}),1e3)}clearCurrentDragSourceNode(){if(this.currentDragSourceNode){var e;if(this.currentDragSourceNode=null,this.rootElement)null===(e=this.window)||void 0===e||e.clearTimeout(this.mouseMoveTimeoutTimer||void 0),this.rootElement.removeEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0);return this.mouseMoveTimeoutTimer=null,!0}return!1}handleDragStart(e,t){e.defaultPrevented||(this.dragStartSourceIds||(this.dragStartSourceIds=[]),this.dragStartSourceIds.unshift(t))}handleDragEnter(e,t){this.dragEnterTargetIds.unshift(t)}handleDragOver(e,t){null===this.dragOverTargetIds&&(this.dragOverTargetIds=[]),this.dragOverTargetIds.unshift(t)}handleDrop(e,t){this.dropTargetIds.unshift(t)}constructor(e,t,n){this.sourcePreviewNodes=new Map,this.sourcePreviewNodeOptions=new Map,this.sourceNodes=new Map,this.sourceNodeOptions=new Map,this.dragStartSourceIds=null,this.dropTargetIds=[],this.dragEnterTargetIds=[],this.currentNativeSource=null,this.currentNativeHandle=null,this.currentDragSourceNode=null,this.altKeyPressed=!1,this.mouseMoveTimeoutTimer=null,this.asyncEndDragFrameId=null,this.dragOverTargetIds=null,this.lastClientOffset=null,this.hoverRafId=null,this.getSourceClientOffset=e=>{const t=this.sourceNodes.get(e);return t&&_t(t)||null},this.endDragNativeItem=()=>{this.isDraggingNativeItem()&&(this.actions.endDrag(),this.currentNativeHandle&&this.registry.removeSource(this.currentNativeHandle),this.currentNativeHandle=null,this.currentNativeSource=null)},this.isNodeInDocument=e=>Boolean(e&&this.document&&this.document.body&&this.document.body.contains(e)),this.endDragIfSourceWasRemovedFromDOM=()=>{const e=this.currentDragSourceNode;null==e||this.isNodeInDocument(e)||(this.clearCurrentDragSourceNode()&&this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover())},this.scheduleHover=e=>{null===this.hoverRafId&&"undefined"!==typeof requestAnimationFrame&&(this.hoverRafId=requestAnimationFrame((()=>{this.monitor.isDragging()&&this.actions.hover(e||[],{clientOffset:this.lastClientOffset}),this.hoverRafId=null})))},this.cancelHover=()=>{null!==this.hoverRafId&&"undefined"!==typeof cancelAnimationFrame&&(cancelAnimationFrame(this.hoverRafId),this.hoverRafId=null)},this.handleTopDragStartCapture=()=>{this.clearCurrentDragSourceNode(),this.dragStartSourceIds=[]},this.handleTopDragStart=e=>{if(e.defaultPrevented)return;const{dragStartSourceIds:t}=this;this.dragStartSourceIds=null;const n=Pt(e);this.monitor.isDragging()&&(this.actions.endDrag(),this.cancelHover()),this.actions.beginDrag(t||[],{publishSource:!1,getSourceClientOffset:this.getSourceClientOffset,clientOffset:n});const{dataTransfer:r}=e,a=Ot(r);if(this.monitor.isDragging()){if(r&&"function"===typeof r.setDragImage){const e=this.monitor.getSourceId(),t=this.sourceNodes.get(e),a=this.sourcePreviewNodes.get(e)||t;if(a){const{anchorX:e,anchorY:o,offsetX:i,offsetY:l}=this.getCurrentSourcePreviewNodeOptions(),s=zt(t,a,n,{anchorX:e,anchorY:o},{offsetX:i,offsetY:l});r.setDragImage(a,s.x,s.y)}}try{null===r||void 0===r||r.setData("application/json",{})}catch(o){}this.setCurrentDragSourceNode(e.target);const{captureDraggingState:t}=this.getCurrentSourcePreviewNodeOptions();t?this.actions.publishDragSource():setTimeout((()=>this.actions.publishDragSource()),0)}else if(a)this.beginDragNativeItem(a);else{if(r&&!r.types&&(e.target&&!e.target.hasAttribute||!e.target.hasAttribute("draggable")))return;e.preventDefault()}},this.handleTopDragEndCapture=()=>{this.clearCurrentDragSourceNode()&&this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover()},this.handleTopDragEnterCapture=e=>{var t;(this.dragEnterTargetIds=[],this.isDraggingNativeItem())&&(null===(t=this.currentNativeSource)||void 0===t||t.loadDataTransfer(e.dataTransfer));if(!this.enterLeaveCounter.enter(e.target)||this.monitor.isDragging())return;const{dataTransfer:n}=e,r=Ot(n);r&&this.beginDragNativeItem(r,n)},this.handleTopDragEnter=e=>{const{dragEnterTargetIds:t}=this;if(this.dragEnterTargetIds=[],!this.monitor.isDragging())return;this.altKeyPressed=e.altKey,t.length>0&&this.actions.hover(t,{clientOffset:Pt(e)});t.some((e=>this.monitor.canDropOnTarget(e)))&&(e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect=this.getCurrentDropEffect()))},this.handleTopDragOverCapture=e=>{var t;(this.dragOverTargetIds=[],this.isDraggingNativeItem())&&(null===(t=this.currentNativeSource)||void 0===t||t.loadDataTransfer(e.dataTransfer))},this.handleTopDragOver=e=>{const{dragOverTargetIds:t}=this;if(this.dragOverTargetIds=[],!this.monitor.isDragging())return e.preventDefault(),void(e.dataTransfer&&(e.dataTransfer.dropEffect="none"));this.altKeyPressed=e.altKey,this.lastClientOffset=Pt(e),this.scheduleHover(t);(t||[]).some((e=>this.monitor.canDropOnTarget(e)))?(e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect=this.getCurrentDropEffect())):this.isDraggingNativeItem()?e.preventDefault():(e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect="none"))},this.handleTopDragLeaveCapture=e=>{this.isDraggingNativeItem()&&e.preventDefault();this.enterLeaveCounter.leave(e.target)&&(this.isDraggingNativeItem()&&setTimeout((()=>this.endDragNativeItem()),0),this.cancelHover())},this.handleTopDropCapture=e=>{var t;(this.dropTargetIds=[],this.isDraggingNativeItem())?(e.preventDefault(),null===(t=this.currentNativeSource)||void 0===t||t.loadDataTransfer(e.dataTransfer)):Ot(e.dataTransfer)&&e.preventDefault();this.enterLeaveCounter.reset()},this.handleTopDrop=e=>{const{dropTargetIds:t}=this;this.dropTargetIds=[],this.actions.hover(t,{clientOffset:Pt(e)}),this.actions.drop({dropEffect:this.getCurrentDropEffect()}),this.isDraggingNativeItem()?this.endDragNativeItem():this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover()},this.handleSelectStart=e=>{const t=e.target;"function"===typeof t.dragDrop&&("INPUT"===t.tagName||"SELECT"===t.tagName||"TEXTAREA"===t.tagName||t.isContentEditable||(e.preventDefault(),t.dragDrop()))},this.options=new Ft(t,n),this.actions=e.getActions(),this.monitor=e.getMonitor(),this.registry=e.getRegistry(),this.enterLeaveCounter=new xt(this.isNodeInDocument)}}const At=function(e,t,n){return new Mt(e,t,n)},Ht=e=>{let t;const n=new Set,r=(e,r)=>{const a="function"===typeof e?e(t):e;if(!Object.is(a,t)){const e=t;t=(null!=r?r:"object"!==typeof a||null===a)?a:Object.assign({},t,a),n.forEach((n=>n(t,e)))}},a=()=>t,o={setState:r,getState:a,getInitialState:()=>i,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},i=t=e(r,a,o);return o},Ut=e=>e?Ht(e):Ht;var Bt=n(443);const{useDebugValue:Wt}=t,{useSyncExternalStoreWithSelector:Vt}=Bt;let $t=!1;const Yt=e=>e;const Qt=e=>{"function"!==typeof e&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const t="function"===typeof e?Ut(e):e,n=(e,n)=>function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Yt,n=arguments.length>2?arguments[2]:void 0;n&&!$t&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),$t=!0);const r=Vt(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return Wt(r),r}(t,e,n);return Object.assign(n,t),n},qt=e=>e?Qt(e):Qt;var Kt=Symbol.for("immer-nothing"),Xt=Symbol.for("immer-draftable"),Gt=Symbol.for("immer-state");function Zt(e){throw new Error("[Immer] minified error nr: ".concat(e,". Full error at: https://bit.ly/3cXEKWf"))}var Jt=Object.getPrototypeOf;function en(e){return!!e&&!!e[Gt]}function tn(e){var t;return!!e&&(rn(e)||Array.isArray(e)||!!e[Xt]||!(null===(t=e.constructor)||void 0===t||!t[Xt])||cn(e)||un(e))}var nn=Object.prototype.constructor.toString();function rn(e){if(!e||"object"!==typeof e)return!1;const t=Jt(e);if(null===t)return!0;const n=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return n===Object||"function"==typeof n&&Function.toString.call(n)===nn}function an(e,t){0===on(e)?Reflect.ownKeys(e).forEach((n=>{t(n,e[n],e)})):e.forEach(((n,r)=>t(r,n,e)))}function on(e){const t=e[Gt];return t?t.type_:Array.isArray(e)?1:cn(e)?2:un(e)?3:0}function ln(e,t){return 2===on(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function sn(e,t,n){const r=on(e);2===r?e.set(t,n):3===r?e.add(n):e[t]=n}function cn(e){return e instanceof Map}function un(e){return e instanceof Set}function dn(e){return e.copy_||e.base_}function fn(e,t){if(cn(e))return new Map(e);if(un(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const n=rn(e);if(!0===t||"class_only"===t&&!n){const t=Object.getOwnPropertyDescriptors(e);delete t[Gt];let n=Reflect.ownKeys(t);for(let r=0;r<n.length;r++){const a=n[r],o=t[a];!1===o.writable&&(o.writable=!0,o.configurable=!0),(o.get||o.set)&&(t[a]={configurable:!0,writable:!0,enumerable:o.enumerable,value:e[a]})}return Object.create(Jt(e),t)}{const t=Jt(e);if(null!==t&&n)return s({},e);const r=Object.create(t);return Object.assign(r,e)}}function pn(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return hn(e)||en(e)||!tn(e)||(on(e)>1&&(e.set=e.add=e.clear=e.delete=gn),Object.freeze(e),t&&Object.entries(e).forEach((e=>{let[t,n]=e;return pn(n,!0)}))),e}function gn(){Zt(2)}function hn(e){return Object.isFrozen(e)}var mn,vn={};function yn(e){const t=vn[e];return t||Zt(0),t}function bn(){return mn}function xn(e,t){t&&(yn("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function wn(e){Sn(e),e.drafts_.forEach(Cn),e.drafts_=null}function Sn(e){e===mn&&(mn=e.parent_)}function kn(e){return mn={drafts_:[],parent_:mn,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function Cn(e){const t=e[Gt];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function En(e,t){t.unfinalizedDrafts_=t.drafts_.length;const n=t.drafts_[0];return void 0!==e&&e!==n?(n[Gt].modified_&&(wn(t),Zt(4)),tn(e)&&(e=Tn(t,e),t.parent_||On(t,e)),t.patches_&&yn("Patches").generateReplacementPatches_(n[Gt].base_,e,t.patches_,t.inversePatches_)):e=Tn(t,n,[]),wn(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==Kt?e:void 0}function Tn(e,t,n){if(hn(t))return t;const r=t[Gt];if(!r)return an(t,((a,o)=>Dn(e,r,t,a,o,n))),t;if(r.scope_!==e)return t;if(!r.modified_)return On(e,r.base_,!0),r.base_;if(!r.finalized_){r.finalized_=!0,r.scope_.unfinalizedDrafts_--;const t=r.copy_;let a=t,o=!1;3===r.type_&&(a=new Set(t),t.clear(),o=!0),an(a,((a,i)=>Dn(e,r,t,a,i,n,o))),On(e,t,!1),n&&e.patches_&&yn("Patches").generatePatches_(r,n,e.patches_,e.inversePatches_)}return r.copy_}function Dn(e,t,n,r,a,o,i){if(en(a)){const i=Tn(e,a,o&&t&&3!==t.type_&&!ln(t.assigned_,r)?o.concat(r):void 0);if(sn(n,r,i),!en(i))return;e.canAutoFreeze_=!1}else i&&n.add(a);if(tn(a)&&!hn(a)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;Tn(e,a),t&&t.scope_.parent_||"symbol"===typeof r||!Object.prototype.propertyIsEnumerable.call(n,r)||On(e,a)}}function On(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&pn(t,n)}var jn={get(e,t){if(t===Gt)return e;const n=dn(e);if(!ln(n,t))return function(e,t,n){var r;const a=_n(t,n);return a?"value"in a?a.value:null===(r=a.get)||void 0===r?void 0:r.call(e.draft_):void 0}(e,n,t);const r=n[t];return e.finalized_||!tn(r)?r:r===In(e.base_,t)?(zn(e),e.copy_[t]=Fn(r,e)):r},has:(e,t)=>t in dn(e),ownKeys:e=>Reflect.ownKeys(dn(e)),set(e,t,n){const r=_n(dn(e),t);if(null!==r&&void 0!==r&&r.set)return r.set.call(e.draft_,n),!0;if(!e.modified_){const r=In(dn(e),t),i=null===r||void 0===r?void 0:r[Gt];if(i&&i.base_===n)return e.copy_[t]=n,e.assigned_[t]=!1,!0;if(((a=n)===(o=r)?0!==a||1/a===1/o:a!==a&&o!==o)&&(void 0!==n||ln(e.base_,t)))return!0;zn(e),Pn(e)}var a,o;return e.copy_[t]===n&&(void 0!==n||t in e.copy_)||Number.isNaN(n)&&Number.isNaN(e.copy_[t])||(e.copy_[t]=n,e.assigned_[t]=!0),!0},deleteProperty:(e,t)=>(void 0!==In(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,zn(e),Pn(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){const n=dn(e),r=Reflect.getOwnPropertyDescriptor(n,t);return r?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:r.enumerable,value:n[t]}:r},defineProperty(){Zt(11)},getPrototypeOf:e=>Jt(e.base_),setPrototypeOf(){Zt(12)}},Nn={};function In(e,t){const n=e[Gt];return(n?dn(n):e)[t]}function _n(e,t){if(!(t in e))return;let n=Jt(e);for(;n;){const e=Object.getOwnPropertyDescriptor(n,t);if(e)return e;n=Jt(n)}}function Pn(e){e.modified_||(e.modified_=!0,e.parent_&&Pn(e.parent_))}function zn(e){e.copy_||(e.copy_=fn(e.base_,e.scope_.immer_.useStrictShallowCopy_))}an(jn,((e,t)=>{Nn[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}})),Nn.deleteProperty=function(e,t){return Nn.set.call(this,e,t,void 0)},Nn.set=function(e,t,n){return jn.set.call(this,e[0],t,n,e[0])};function Fn(e,t){const n=cn(e)?yn("MapSet").proxyMap_(e,t):un(e)?yn("MapSet").proxySet_(e,t):function(e,t){const n=Array.isArray(e),r={type_:n?1:0,scope_:t?t.scope_:bn(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let a=r,o=jn;n&&(a=[r],o=Nn);const{revoke:i,proxy:l}=Proxy.revocable(a,o);return r.draft_=l,r.revoke_=i,l}(e,t);return(t?t.scope_:bn()).drafts_.push(n),n}function Ln(e){if(!tn(e)||hn(e))return e;const t=e[Gt];let n;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,n=fn(e,t.scope_.immer_.useStrictShallowCopy_)}else n=fn(e,!0);return an(n,((e,t)=>{sn(n,e,Ln(t))})),t&&(t.finalized_=!1),n}var Rn=new class{constructor(e){var t=this;this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,n)=>{if("function"===typeof e&&"function"!==typeof t){const n=t;t=e;const r=this;return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n;for(var a=arguments.length,o=new Array(a>1?a-1:0),i=1;i<a;i++)o[i-1]=arguments[i];return r.produce(e,(e=>t.call(this,e,...o)))}}let r;if("function"!==typeof t&&Zt(6),void 0!==n&&"function"!==typeof n&&Zt(7),tn(e)){const a=kn(this),o=Fn(e,void 0);let i=!0;try{r=t(o),i=!1}finally{i?wn(a):Sn(a)}return xn(a,n),En(r,a)}if(!e||"object"!==typeof e){if(r=t(e),void 0===r&&(r=e),r===Kt&&(r=void 0),this.autoFreeze_&&pn(r,!0),n){const t=[],a=[];yn("Patches").generateReplacementPatches_(e,r,t,a),n(t,a)}return r}Zt(1)},this.produceWithPatches=(e,n)=>{if("function"===typeof e)return function(n){for(var r=arguments.length,a=new Array(r>1?r-1:0),o=1;o<r;o++)a[o-1]=arguments[o];return t.produceWithPatches(n,(t=>e(t,...a)))};let r,a;return[this.produce(e,n,((e,t)=>{r=e,a=t})),r,a]},"boolean"===typeof(null===e||void 0===e?void 0:e.autoFreeze)&&this.setAutoFreeze(e.autoFreeze),"boolean"===typeof(null===e||void 0===e?void 0:e.useStrictShallowCopy)&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){tn(e)||Zt(8),en(e)&&(e=function(e){en(e)||Zt(10);return Ln(e)}(e));const t=kn(this),n=Fn(e,void 0);return n[Gt].isManual_=!0,Sn(t),n}finishDraft(e,t){const n=e&&e[Gt];n&&n.isManual_||Zt(9);const{scope_:r}=n;return xn(r,t),En(void 0,r)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let n;for(n=t.length-1;n>=0;n--){const r=t[n];if(0===r.path.length&&"replace"===r.op){e=r.value;break}}n>-1&&(t=t.slice(n+1));const r=yn("Patches").applyPatches_;return en(e)?r(e,t):this.produce(e,(e=>r(e,t)))}},Mn=Rn.produce;Rn.produceWithPatches.bind(Rn),Rn.setAutoFreeze.bind(Rn),Rn.setUseStrictShallowCopy.bind(Rn),Rn.applyPatches.bind(Rn),Rn.createDraft.bind(Rn),Rn.finishDraft.bind(Rn);const An=e=>(t,n,r)=>(r.setState=function(e,n){const r="function"===typeof e?Mn(e):e;for(var a=arguments.length,o=new Array(a>2?a-2:0),i=2;i<a;i++)o[i-2]=arguments[i];return t(r,n,...o)},e(r.setState,n,r)),Hn=qt(An(((e,t)=>({elements:[],connections:[],viewport:{x:0,y:0,zoom:1},selectedElements:[],addElement:t=>e((e=>{e.elements.push(t)})),updateElement:(t,n)=>e((e=>{const r=e.elements.findIndex((e=>e.id===t));-1!==r&&Object.assign(e.elements[r],n)})),removeElement:t=>e((e=>{e.elements=e.elements.filter((e=>e.id!==t)),e.connections=e.connections.filter((e=>e.sourceId!==t&&e.targetId!==t))})),updateViewport:t=>e((e=>{Object.assign(e.viewport,t)})),connectElements:function(t,n){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"default";return e((e=>{e.connections.find((e=>e.sourceId===t&&e.targetId===n))||e.connections.push({id:"conn-".concat(t,"-").concat(n,"-").concat(Date.now()),sourceId:t,targetId:n,type:r,createdAt:(new Date).toISOString()})}))},disconnectElements:(t,n)=>e((e=>{e.connections=e.connections.filter((e=>!(e.sourceId===t&&e.targetId===n)))})),clearCanvas:()=>e((e=>{e.elements=[],e.connections=[],e.selectedElements=[]})),loadProject:t=>e((e=>{e.elements=t.elements||[],e.connections=t.connections||[],e.viewport=t.viewport||{x:0,y:0,zoom:1}})),exportProject:()=>{const e=t();return{elements:e.elements,connections:e.connections,viewport:e.viewport,exportedAt:(new Date).toISOString()}}}))));var Un=n(122);const Bn="undefined"!==typeof window?t.useLayoutEffect:t.useEffect;function Wn(e,n,r){const[a,o]=function(e,n,r){const[a,o]=(0,t.useState)((()=>n(e))),i=(0,t.useCallback)((()=>{const t=n(e);Un(a,t)||(o(t),r&&r())}),[a,e,r]);return Bn(i),[a,i]}(e,n,r);return Bn((function(){const t=e.getHandlerId();if(null!=t)return e.subscribeToStateChange(o,{handlerIds:[t]})}),[e,o]),a}function Vn(e,t,n){return Wn(t,e||(()=>({})),(()=>n.reconnect()))}function $n(e,n){const r=[...n||[]];return null==n&&"function"!==typeof e&&r.push(e),(0,t.useMemo)((()=>"function"===typeof e?e():e),r)}function Yn(e){return(0,t.useMemo)((()=>e.hooks.dropTarget()),[e])}function Qn(e,t,n,r){let a=n?n.call(r,e,t):void 0;if(void 0!==a)return!!a;if(e===t)return!0;if("object"!==typeof e||!e||"object"!==typeof t||!t)return!1;const o=Object.keys(e),i=Object.keys(t);if(o.length!==i.length)return!1;const l=Object.prototype.hasOwnProperty.bind(t);for(let s=0;s<o.length;s++){const i=o[s];if(!l(i))return!1;const c=e[i],u=t[i];if(a=n?n.call(r,c,u,i):void 0,!1===a||void 0===a&&c!==u)return!1}return!0}function qn(e){return null!==e&&"object"===typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function Kn(e){return function(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(!(0,t.isValidElement)(n)){const t=n;return e(t,r),t}const a=n;!function(e){if("string"===typeof e.type)return;const t=e.type.displayName||e.type.name||"the component";throw new Error("Only native element nodes can now be passed to React DnD connectors."+"You can either wrap ".concat(t," into a <div>, or turn it into a ")+"drag source or a drop target itself.")}(a);return function(e,n){const r=e.ref;return de("string"!==typeof r,"Cannot connect React DnD to an element with an existing string ref. Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. Read more: https://reactjs.org/docs/refs-and-the-dom.html#callback-refs"),r?(0,t.cloneElement)(e,{ref:e=>{Gn(r,e),Gn(n,e)}}):(0,t.cloneElement)(e,{ref:n})}(a,r?t=>e(t,r):e)}}function Xn(e){const t={};return Object.keys(e).forEach((n=>{const r=e[n];if(n.endsWith("Ref"))t[n]=e[n];else{const e=Kn(r);t[n]=()=>e}})),t}function Gn(e,t){"function"===typeof e?e(t):e.current=t}class Zn{get connectTarget(){return this.dropTarget}reconnect(){const e=this.didHandlerIdChange()||this.didDropTargetChange()||this.didOptionsChange();e&&this.disconnectDropTarget();const t=this.dropTarget;this.handlerId&&(t?e&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDropTarget=t,this.lastConnectedDropTargetOptions=this.dropTargetOptions,this.unsubscribeDropTarget=this.backend.connectDropTarget(this.handlerId,t,this.dropTargetOptions)):this.lastConnectedDropTarget=t)}receiveHandlerId(e){e!==this.handlerId&&(this.handlerId=e,this.reconnect())}get dropTargetOptions(){return this.dropTargetOptionsInternal}set dropTargetOptions(e){this.dropTargetOptionsInternal=e}didHandlerIdChange(){return this.lastConnectedHandlerId!==this.handlerId}didDropTargetChange(){return this.lastConnectedDropTarget!==this.dropTarget}didOptionsChange(){return!Qn(this.lastConnectedDropTargetOptions,this.dropTargetOptions)}disconnectDropTarget(){this.unsubscribeDropTarget&&(this.unsubscribeDropTarget(),this.unsubscribeDropTarget=void 0)}get dropTarget(){return this.dropTargetNode||this.dropTargetRef&&this.dropTargetRef.current}clearDropTarget(){this.dropTargetRef=null,this.dropTargetNode=null}constructor(e){this.hooks=Xn({dropTarget:(e,t)=>{this.clearDropTarget(),this.dropTargetOptions=t,qn(e)?this.dropTargetRef=e:this.dropTargetNode=e,this.reconnect()}}),this.handlerId=null,this.dropTargetRef=null,this.dropTargetOptionsInternal=null,this.lastConnectedHandlerId=null,this.lastConnectedDropTarget=null,this.lastConnectedDropTargetOptions=null,this.backend=e}}function Jn(){const{dragDropManager:e}=(0,t.useContext)(pt);return de(null!=e,"Expected drag drop context"),e}let er=!1;class tr{receiveHandlerId(e){this.targetId=e}getHandlerId(){return this.targetId}subscribeToStateChange(e,t){return this.internalMonitor.subscribeToStateChange(e,t)}canDrop(){if(!this.targetId)return!1;de(!er,"You may not call monitor.canDrop() inside your canDrop() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target-monitor");try{return er=!0,this.internalMonitor.canDropOnTarget(this.targetId)}finally{er=!1}}isOver(e){return!!this.targetId&&this.internalMonitor.isOverTarget(this.targetId,e)}getItemType(){return this.internalMonitor.getItemType()}getItem(){return this.internalMonitor.getItem()}getDropResult(){return this.internalMonitor.getDropResult()}didDrop(){return this.internalMonitor.didDrop()}getInitialClientOffset(){return this.internalMonitor.getInitialClientOffset()}getInitialSourceClientOffset(){return this.internalMonitor.getInitialSourceClientOffset()}getSourceClientOffset(){return this.internalMonitor.getSourceClientOffset()}getClientOffset(){return this.internalMonitor.getClientOffset()}getDifferenceFromInitialOffset(){return this.internalMonitor.getDifferenceFromInitialOffset()}constructor(e){this.targetId=null,this.internalMonitor=e.getMonitor()}}class nr{canDrop(){const e=this.spec,t=this.monitor;return!e.canDrop||e.canDrop(t.getItem(),t)}hover(){const e=this.spec,t=this.monitor;e.hover&&e.hover(t.getItem(),t)}drop(){const e=this.spec,t=this.monitor;if(e.drop)return e.drop(t.getItem(),t)}constructor(e,t){this.spec=e,this.monitor=t}}function rr(e,n,r){const a=Jn(),o=function(e,n){const r=(0,t.useMemo)((()=>new nr(e,n)),[n]);return(0,t.useEffect)((()=>{r.spec=e}),[e]),r}(e,n),i=function(e){const{accept:n}=e;return(0,t.useMemo)((()=>(de(null!=e.accept,"accept must be defined"),Array.isArray(n)?n:[n])),[n])}(e);Bn((function(){const[e,t]=function(e,t,n){const r=n.getRegistry(),a=r.addTarget(e,t);return[a,()=>r.removeTarget(a)]}(i,o,a);return n.receiveHandlerId(e),r.receiveHandlerId(e),t}),[a,n,o,r,i.map((e=>e.toString())).join("|")])}function ar(e,n){const r=$n(e,n),a=function(){const e=Jn();return(0,t.useMemo)((()=>new tr(e)),[e])}(),o=function(e){const n=Jn(),r=(0,t.useMemo)((()=>new Zn(n.getBackend())),[n]);return Bn((()=>(r.dropTargetOptions=e||null,r.reconnect(),()=>r.disconnectDropTarget())),[e]),r}(r.options);return rr(r,a,o),[Vn(r.collect,a,o),Yn(o)]}const or=e=>{var n,r;let{data:a,isSelected:o,onUpdate:i}=e;const[l,c]=(0,t.useState)(!1),[u,d]=(0,t.useState)(a);return(0,y.jsxs)("div",{className:"canvas-element element-smart-goal ".concat(o?"selected":""),children:[(0,y.jsxs)("div",{className:"element-header",children:[l?(0,y.jsx)("input",{type:"text",value:u.title,onChange:e=>d(s(s({},u),{},{title:e.target.value})),className:"title-input",placeholder:"Goal title"}):(0,y.jsx)("h3",{className:"element-title",children:a.title||"New SMART Goal"}),(0,y.jsxs)("div",{className:"header-actions",children:[(0,y.jsx)("span",{className:"element-type-badge",children:"SMART Goal"}),l?(0,y.jsxs)("div",{className:"edit-actions",children:[(0,y.jsx)("button",{className:"save-button",onClick:e=>{e.stopPropagation(),i&&i(u),c(!1)},children:"\u2713"}),(0,y.jsx)("button",{className:"cancel-button",onClick:e=>{e.stopPropagation(),d(a),c(!1)},children:"\u2715"})]}):(0,y.jsx)("button",{className:"edit-button",onClick:e=>{e.stopPropagation(),c(!0)},children:"\u270f\ufe0f"})]})]}),(0,y.jsxs)("div",{className:"element-content",children:[(0,y.jsxs)("div",{className:"smart-criteria",children:[(0,y.jsxs)("div",{className:"criteria-item",children:[(0,y.jsx)("div",{className:"field-label",children:"Specific"}),l?(0,y.jsx)("textarea",{value:u.specific||"",onChange:e=>d(s(s({},u),{},{specific:e.target.value})),placeholder:"What exactly will be accomplished?",rows:"2"}):(0,y.jsx)("div",{className:"field-value",children:a.specific||"Not defined"})]}),(0,y.jsxs)("div",{className:"criteria-item",children:[(0,y.jsx)("div",{className:"field-label",children:"Measurable"}),l?(0,y.jsx)("textarea",{value:u.measurable||"",onChange:e=>d(s(s({},u),{},{measurable:e.target.value})),placeholder:"How will progress be measured?",rows:"2"}):(0,y.jsx)("div",{className:"field-value",children:a.measurable||"Not defined"})]}),(0,y.jsxs)("div",{className:"criteria-item",children:[(0,y.jsx)("div",{className:"field-label",children:"Time-bound"}),l?(0,y.jsx)("input",{type:"date",value:(null===(n=u.timeBound)||void 0===n?void 0:n.endDate)||"",onChange:e=>d(s(s({},u),{},{timeBound:s(s({},u.timeBound),{},{endDate:e.target.value})})),placeholder:"End date"}):(0,y.jsxs)("div",{className:"field-value",children:["End: ",(p=null===(r=a.timeBound)||void 0===r?void 0:r.endDate,p?new Date(p).toLocaleDateString():"No deadline")]})]})]}),(0,y.jsxs)("div",{className:"progress-section",children:[(0,y.jsxs)("div",{className:"progress-header",children:[(0,y.jsx)("span",{className:"field-label",children:"Progress"}),l?(0,y.jsx)("input",{type:"number",min:"0",max:"100",value:u.progress||0,onChange:e=>d(s(s({},u),{},{progress:parseInt(e.target.value)||0})),className:"progress-input"}):(0,y.jsxs)("span",{className:"progress-value",children:[a.progress||0,"%"]})]}),(0,y.jsx)("div",{className:"progress-bar",children:(0,y.jsx)("div",{className:"progress-fill",style:{width:"".concat((l?u.progress:a.progress)||0,"%"),backgroundColor:(f=(l?u.progress:a.progress)||0,f>=80?"#28a745":f>=50?"#ffc107":f>=25?"#fd7e14":"#dc3545")}})})]})]}),(0,y.jsx)("style",{jsx:!0,children:'\n        .title-input {\n          width: 100%;\n          border: none;\n          background: transparent;\n          font-size: 14px;\n          font-weight: 600;\n          color: #495057;\n          padding: 0;\n        }\n        \n        .title-input:focus {\n          outline: 1px solid #007bff;\n          border-radius: 2px;\n          padding: 2px;\n        }\n        \n        .header-actions {\n          display: flex;\n          align-items: center;\n          gap: 8px;\n        }\n        \n        .edit-button {\n          background: none;\n          border: none;\n          cursor: pointer;\n          padding: 4px;\n          border-radius: 3px;\n          opacity: 0.7;\n        }\n        \n        .edit-button:hover {\n          background: rgba(0, 0, 0, 0.1);\n          opacity: 1;\n        }\n        \n        .edit-actions {\n          display: flex;\n          gap: 4px;\n        }\n        \n        .save-button,\n        .cancel-button {\n          background: none;\n          border: none;\n          cursor: pointer;\n          padding: 4px;\n          border-radius: 3px;\n          font-size: 12px;\n        }\n        \n        .save-button {\n          color: #28a745;\n        }\n        \n        .save-button:hover {\n          background: rgba(40, 167, 69, 0.1);\n        }\n        \n        .cancel-button {\n          color: #dc3545;\n        }\n        \n        .cancel-button:hover {\n          background: rgba(220, 53, 69, 0.1);\n        }\n        \n        .smart-criteria {\n          margin-bottom: 16px;\n        }\n        \n        .criteria-item {\n          margin-bottom: 12px;\n        }\n        \n        .criteria-item:last-child {\n          margin-bottom: 0;\n        }\n        \n        .criteria-item textarea {\n          width: 100%;\n          border: 1px solid #e1e5e9;\n          border-radius: 4px;\n          padding: 6px 8px;\n          font-size: 12px;\n          resize: vertical;\n          min-height: 40px;\n        }\n        \n        .criteria-item textarea:focus {\n          outline: none;\n          border-color: #007bff;\n          box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n        }\n        \n        .criteria-item input[type="date"] {\n          width: 100%;\n          border: 1px solid #e1e5e9;\n          border-radius: 4px;\n          padding: 4px 6px;\n          font-size: 12px;\n        }\n        \n        .progress-section {\n          border-top: 1px solid #e1e5e9;\n          padding-top: 12px;\n        }\n        \n        .progress-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 8px;\n        }\n        \n        .progress-value {\n          font-weight: 600;\n          color: #495057;\n        }\n        \n        .progress-input {\n          width: 60px;\n          border: 1px solid #e1e5e9;\n          border-radius: 4px;\n          padding: 2px 6px;\n          font-size: 12px;\n          text-align: center;\n        }\n        \n        .progress-bar {\n          width: 100%;\n          height: 8px;\n          background: #e9ecef;\n          border-radius: 4px;\n          overflow: hidden;\n        }\n        \n        .progress-fill {\n          height: 100%;\n          transition: width 0.3s ease, background-color 0.3s ease;\n        }\n      '})]});var f,p},ir=e=>{let{elements:n,selectedElements:r,onElementSelect:a,onElementMove:o,onElementDrop:i,viewport:l,onViewportChange:s}=e;const c=(0,t.useRef)(null),[{isOver:u},d]=ar({accept:["smart-goal","task","kpi","brand-asset"],drop:(e,t)=>{const n=t.getClientOffset(),r=c.current.getBoundingClientRect(),a={x:(n.x-r.left-l.x)/l.zoom,y:(n.y-r.top-l.y)/l.zoom};i&&i(e.type,a)},collect:e=>({isOver:e.isOver()})});return(0,y.jsxs)("div",{ref:e=>{c.current=e,d(e)},className:"canvas-grid ".concat(u?"drop-target":""),style:{position:"relative",width:"100%",height:"100%",overflow:"hidden"},children:[n.map((e=>{var t,n;const i=r.includes(e.id),s={position:"absolute",left:e.position.x+l.x,top:e.position.y+l.y,transform:"scale(".concat(l.zoom,")"),transformOrigin:"top left",zIndex:i?100:1},c=t=>{t.stopPropagation(),a(e.id,t.ctrlKey||t.metaKey)},u=t=>{if(0!==t.button)return;const n=t.clientX,r=t.clientY,a=e.position.x,i=e.position.y,s=t=>{const s=t.clientX-n,c=t.clientY-r,u={x:a+s/l.zoom,y:i+c/l.zoom};o(e.id,u)},c=()=>{document.removeEventListener("mousemove",s),document.removeEventListener("mouseup",c)};document.addEventListener("mousemove",s),document.addEventListener("mouseup",c)};switch(e.type){case"smart-goal":return(0,y.jsx)("div",{style:s,onClick:c,onMouseDown:u,children:(0,y.jsx)(or,{data:e.data,isSelected:i})},e.id);case"task":return(0,y.jsxs)("div",{style:s,onClick:c,onMouseDown:u,className:"canvas-element element-task ".concat(i?"selected":""),children:[(0,y.jsxs)("div",{className:"element-header",children:[(0,y.jsx)("h3",{className:"element-title",children:e.data.title}),(0,y.jsx)("span",{className:"element-type-badge",children:"Task"})]}),(0,y.jsxs)("div",{className:"element-content",children:[(0,y.jsxs)("div",{className:"element-field",children:[(0,y.jsx)("div",{className:"field-label",children:"Description"}),(0,y.jsx)("div",{className:"field-value",children:e.data.description||"No description"})]}),(0,y.jsxs)("div",{className:"element-field",children:[(0,y.jsx)("div",{className:"field-label",children:"Status"}),(0,y.jsxs)("div",{className:"status-indicator status-".concat(e.data.status),children:[(0,y.jsx)("div",{className:"status-dot"}),(null===(t=e.data.status)||void 0===t?void 0:t.replace("-"," "))||"Not started"]})]}),(0,y.jsxs)("div",{className:"element-field",children:[(0,y.jsx)("div",{className:"field-label",children:"Priority"}),(0,y.jsx)("div",{className:"priority-indicator priority-".concat(e.data.priority),children:e.data.priority||"Medium"})]})]})]},e.id);case"kpi":return(0,y.jsxs)("div",{style:s,onClick:c,onMouseDown:u,className:"canvas-element element-kpi ".concat(i?"selected":""),children:[(0,y.jsxs)("div",{className:"element-header",children:[(0,y.jsx)("h3",{className:"element-title",children:e.data.title}),(0,y.jsx)("span",{className:"element-type-badge",children:"KPI"})]}),(0,y.jsxs)("div",{className:"element-content",children:[(0,y.jsxs)("div",{className:"element-field",children:[(0,y.jsx)("div",{className:"field-label",children:"Current Value"}),(0,y.jsxs)("div",{className:"field-value",children:[e.data.currentValue||0," ",e.data.unit||""]})]}),(0,y.jsxs)("div",{className:"element-field",children:[(0,y.jsx)("div",{className:"field-label",children:"Target Value"}),(0,y.jsxs)("div",{className:"field-value",children:[e.data.targetValue||100," ",e.data.unit||""]})]}),(0,y.jsxs)("div",{className:"element-field",children:[(0,y.jsx)("div",{className:"field-label",children:"Progress"}),(0,y.jsx)("div",{className:"progress-bar",children:(0,y.jsx)("div",{className:"progress-fill",style:{width:"".concat(Math.min(100,e.data.currentValue/e.data.targetValue*100),"%")}})})]})]})]},e.id);case"brand-asset":return(0,y.jsxs)("div",{style:s,onClick:c,onMouseDown:u,className:"canvas-element element-brand-asset ".concat(i?"selected":""),children:[(0,y.jsxs)("div",{className:"element-header",children:[(0,y.jsx)("h3",{className:"element-title",children:e.data.title}),(0,y.jsx)("span",{className:"element-type-badge",children:"Brand Asset"})]}),(0,y.jsxs)("div",{className:"element-content",children:[(0,y.jsxs)("div",{className:"element-field",children:[(0,y.jsx)("div",{className:"field-label",children:"Asset Type"}),(0,y.jsx)("div",{className:"field-value",children:e.data.assetType||"Unknown"})]}),(0,y.jsxs)("div",{className:"element-field",children:[(0,y.jsx)("div",{className:"field-label",children:"Tags"}),(0,y.jsx)("div",{className:"field-value",children:(null===(n=e.data.tags)||void 0===n?void 0:n.join(", "))||"No tags"})]})]})]},e.id);default:return null}})),(0,y.jsx)("style",{jsx:!0,children:"\n        .canvas-grid {\n          cursor: grab;\n        }\n        \n        .canvas-grid:active {\n          cursor: grabbing;\n        }\n        \n        .canvas-grid.drop-target {\n          background-color: rgba(0, 123, 255, 0.05);\n        }\n        \n        .canvas-element {\n          width: 250px;\n          min-height: 180px;\n          cursor: move;\n          user-select: none;\n        }\n        \n        .canvas-element:hover {\n          z-index: 50 !important;\n        }\n      "})]})};function lr(e){return(0,t.useMemo)((()=>e.hooks.dragSource()),[e])}function sr(e){return(0,t.useMemo)((()=>e.hooks.dragPreview()),[e])}class cr{receiveHandlerId(e){this.handlerId!==e&&(this.handlerId=e,this.reconnect())}get connectTarget(){return this.dragSource}get dragSourceOptions(){return this.dragSourceOptionsInternal}set dragSourceOptions(e){this.dragSourceOptionsInternal=e}get dragPreviewOptions(){return this.dragPreviewOptionsInternal}set dragPreviewOptions(e){this.dragPreviewOptionsInternal=e}reconnect(){const e=this.reconnectDragSource();this.reconnectDragPreview(e)}reconnectDragSource(){const e=this.dragSource,t=this.didHandlerIdChange()||this.didConnectedDragSourceChange()||this.didDragSourceOptionsChange();return t&&this.disconnectDragSource(),this.handlerId?e?(t&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragSource=e,this.lastConnectedDragSourceOptions=this.dragSourceOptions,this.dragSourceUnsubscribe=this.backend.connectDragSource(this.handlerId,e,this.dragSourceOptions)),t):(this.lastConnectedDragSource=e,t):t}reconnectDragPreview(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];const t=this.dragPreview,n=e||this.didHandlerIdChange()||this.didConnectedDragPreviewChange()||this.didDragPreviewOptionsChange();n&&this.disconnectDragPreview(),this.handlerId&&(t?n&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragPreview=t,this.lastConnectedDragPreviewOptions=this.dragPreviewOptions,this.dragPreviewUnsubscribe=this.backend.connectDragPreview(this.handlerId,t,this.dragPreviewOptions)):this.lastConnectedDragPreview=t)}didHandlerIdChange(){return this.lastConnectedHandlerId!==this.handlerId}didConnectedDragSourceChange(){return this.lastConnectedDragSource!==this.dragSource}didConnectedDragPreviewChange(){return this.lastConnectedDragPreview!==this.dragPreview}didDragSourceOptionsChange(){return!Qn(this.lastConnectedDragSourceOptions,this.dragSourceOptions)}didDragPreviewOptionsChange(){return!Qn(this.lastConnectedDragPreviewOptions,this.dragPreviewOptions)}disconnectDragSource(){this.dragSourceUnsubscribe&&(this.dragSourceUnsubscribe(),this.dragSourceUnsubscribe=void 0)}disconnectDragPreview(){this.dragPreviewUnsubscribe&&(this.dragPreviewUnsubscribe(),this.dragPreviewUnsubscribe=void 0,this.dragPreviewNode=null,this.dragPreviewRef=null)}get dragSource(){return this.dragSourceNode||this.dragSourceRef&&this.dragSourceRef.current}get dragPreview(){return this.dragPreviewNode||this.dragPreviewRef&&this.dragPreviewRef.current}clearDragSource(){this.dragSourceNode=null,this.dragSourceRef=null}clearDragPreview(){this.dragPreviewNode=null,this.dragPreviewRef=null}constructor(e){this.hooks=Xn({dragSource:(e,t)=>{this.clearDragSource(),this.dragSourceOptions=t||null,qn(e)?this.dragSourceRef=e:this.dragSourceNode=e,this.reconnectDragSource()},dragPreview:(e,t)=>{this.clearDragPreview(),this.dragPreviewOptions=t||null,qn(e)?this.dragPreviewRef=e:this.dragPreviewNode=e,this.reconnectDragPreview()}}),this.handlerId=null,this.dragSourceRef=null,this.dragSourceOptionsInternal=null,this.dragPreviewRef=null,this.dragPreviewOptionsInternal=null,this.lastConnectedHandlerId=null,this.lastConnectedDragSource=null,this.lastConnectedDragSourceOptions=null,this.lastConnectedDragPreview=null,this.lastConnectedDragPreviewOptions=null,this.backend=e}}let ur=!1,dr=!1;class fr{receiveHandlerId(e){this.sourceId=e}getHandlerId(){return this.sourceId}canDrag(){de(!ur,"You may not call monitor.canDrag() inside your canDrag() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return ur=!0,this.internalMonitor.canDragSource(this.sourceId)}finally{ur=!1}}isDragging(){if(!this.sourceId)return!1;de(!dr,"You may not call monitor.isDragging() inside your isDragging() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return dr=!0,this.internalMonitor.isDraggingSource(this.sourceId)}finally{dr=!1}}subscribeToStateChange(e,t){return this.internalMonitor.subscribeToStateChange(e,t)}isDraggingSource(e){return this.internalMonitor.isDraggingSource(e)}isOverTarget(e,t){return this.internalMonitor.isOverTarget(e,t)}getTargetIds(){return this.internalMonitor.getTargetIds()}isSourcePublic(){return this.internalMonitor.isSourcePublic()}getSourceId(){return this.internalMonitor.getSourceId()}subscribeToOffsetChange(e){return this.internalMonitor.subscribeToOffsetChange(e)}canDragSource(e){return this.internalMonitor.canDragSource(e)}canDropOnTarget(e){return this.internalMonitor.canDropOnTarget(e)}getItemType(){return this.internalMonitor.getItemType()}getItem(){return this.internalMonitor.getItem()}getDropResult(){return this.internalMonitor.getDropResult()}didDrop(){return this.internalMonitor.didDrop()}getInitialClientOffset(){return this.internalMonitor.getInitialClientOffset()}getInitialSourceClientOffset(){return this.internalMonitor.getInitialSourceClientOffset()}getSourceClientOffset(){return this.internalMonitor.getSourceClientOffset()}getClientOffset(){return this.internalMonitor.getClientOffset()}getDifferenceFromInitialOffset(){return this.internalMonitor.getDifferenceFromInitialOffset()}constructor(e){this.sourceId=null,this.internalMonitor=e.getMonitor()}}class pr{beginDrag(){const e=this.spec,t=this.monitor;let n=null;return n="object"===typeof e.item?e.item:"function"===typeof e.item?e.item(t):{},null!==n&&void 0!==n?n:null}canDrag(){const e=this.spec,t=this.monitor;return"boolean"===typeof e.canDrag?e.canDrag:"function"!==typeof e.canDrag||e.canDrag(t)}isDragging(e,t){const n=this.spec,r=this.monitor,{isDragging:a}=n;return a?a(r):t===e.getSourceId()}endDrag(){const e=this.spec,t=this.monitor,n=this.connector,{end:r}=e;r&&r(t.getItem(),t),n.reconnect()}constructor(e,t,n){this.spec=e,this.monitor=t,this.connector=n}}function gr(e,n,r){const a=Jn(),o=function(e,n,r){const a=(0,t.useMemo)((()=>new pr(e,n,r)),[n,r]);return(0,t.useEffect)((()=>{a.spec=e}),[e]),a}(e,n,r),i=function(e){return(0,t.useMemo)((()=>{const t=e.type;return de(null!=t,"spec.type must be defined"),t}),[e])}(e);Bn((function(){if(null!=i){const[e,t]=function(e,t,n){const r=n.getRegistry(),a=r.addSource(e,t);return[a,()=>r.removeSource(a)]}(i,o,a);return n.receiveHandlerId(e),r.receiveHandlerId(e),t}}),[a,n,r,o,i])}function hr(e,n){const r=$n(e,n);de(!r.begin,"useDrag::spec.begin was deprecated in v14. Replace spec.begin() with spec.item(). (see more here - https://react-dnd.github.io/react-dnd/docs/api/use-drag)");const a=function(){const e=Jn();return(0,t.useMemo)((()=>new fr(e)),[e])}(),o=function(e,n){const r=Jn(),a=(0,t.useMemo)((()=>new cr(r.getBackend())),[r]);return Bn((()=>(a.dragSourceOptions=e||null,a.reconnect(),()=>a.disconnectDragSource())),[a,e]),Bn((()=>(a.dragPreviewOptions=n||null,a.reconnect(),()=>a.disconnectDragPreview())),[a,n]),a}(r.options,r.previewOptions);return gr(r,a,o),[Vn(r.collect,a,o),lr(o),sr(o)]}const mr=e=>{let{type:t,name:n,description:r,icon:a,onElementDrop:o}=e;const[{isDragging:i},l]=hr({type:t,item:{type:t},end:(e,t)=>{t.getDropResult()},collect:e=>({isDragging:e.isDragging()})});return(0,y.jsxs)("div",{ref:l,className:"component-item ".concat(i?"dragging":""),style:{opacity:i?.5:1},children:[(0,y.jsx)("div",{className:"component-icon",children:a}),(0,y.jsxs)("div",{className:"component-info",children:[(0,y.jsx)("div",{className:"component-name",children:n}),(0,y.jsx)("div",{className:"component-description",children:r})]})]})},vr=e=>{let{onElementDrop:t}=e;const n=[{category:"Planning",items:[{type:"smart-goal",name:"SMART Goal",description:"Specific, Measurable, Achievable, Relevant, Time-bound goal",icon:(0,y.jsx)("svg",{viewBox:"0 0 24 24",fill:"currentColor",children:(0,y.jsx)("path",{d:"M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,16.5L6.5,12L7.91,10.59L11,13.67L16.59,8.09L18,9.5L11,16.5Z"})})},{type:"task",name:"Task",description:"Individual action item or deliverable",icon:(0,y.jsx)("svg",{viewBox:"0 0 24 24",fill:"currentColor",children:(0,y.jsx)("path",{d:"M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V19H5V5H19Z"})})}]},{category:"Metrics",items:[{type:"kpi",name:"KPI",description:"Key Performance Indicator with targets",icon:(0,y.jsx)("svg",{viewBox:"0 0 24 24",fill:"currentColor",children:(0,y.jsx)("path",{d:"M16,6L18.29,8.29L13.41,13.17L9.41,9.17L2,16.59L3.41,18L9.41,12L13.41,16L19.71,9.71L22,12V6H16Z"})})}]},{category:"Assets",items:[{type:"brand-asset",name:"Brand Asset",description:"Brand materials and resources",icon:(0,y.jsx)("svg",{viewBox:"0 0 24 24",fill:"currentColor",children:(0,y.jsx)("path",{d:"M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"})})}]}];return(0,y.jsxs)("div",{className:"component-sidebar",children:[(0,y.jsxs)("div",{className:"sidebar-header",children:[(0,y.jsx)("h2",{children:"Components"}),(0,y.jsx)("p",{children:"Drag components to canvas"})]}),(0,y.jsx)("div",{className:"sidebar-content",children:n.map((e=>(0,y.jsxs)("div",{className:"component-category",children:[(0,y.jsx)("h3",{className:"category-title",children:e.category}),(0,y.jsx)("div",{className:"component-list",children:e.items.map((e=>(0,y.jsx)(mr,{type:e.type,name:e.name,description:e.description,icon:e.icon,onElementDrop:t},e.type)))})]},e.category)))}),(0,y.jsx)("div",{className:"sidebar-footer",children:(0,y.jsxs)("div",{className:"help-text",children:[(0,y.jsxs)("p",{children:[(0,y.jsx)("strong",{children:"Tip:"})," Drag any component to the canvas to create it"]}),(0,y.jsxs)("p",{children:["Hold ",(0,y.jsx)("kbd",{children:"Ctrl"})," to select multiple elements"]}),(0,y.jsxs)("p",{children:["Press ",(0,y.jsx)("kbd",{children:"Delete"})," to remove selected elements"]})]})}),(0,y.jsx)("style",{jsx:!0,children:"\n        .component-sidebar {\n          width: 280px;\n          background: white;\n          border-right: 1px solid #e1e5e9;\n          display: flex;\n          flex-direction: column;\n          box-shadow: 2px 0 4px rgba(0, 0, 0, 0.05);\n          z-index: 10;\n        }\n        \n        .sidebar-header {\n          padding: 20px;\n          border-bottom: 1px solid #e1e5e9;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          color: white;\n        }\n        \n        .sidebar-header h2 {\n          margin: 0 0 4px 0;\n          font-size: 18px;\n          font-weight: 600;\n        }\n        \n        .sidebar-header p {\n          margin: 0;\n          font-size: 14px;\n          opacity: 0.9;\n        }\n        \n        .sidebar-content {\n          flex: 1;\n          padding: 20px;\n          overflow-y: auto;\n        }\n        \n        .component-category {\n          margin-bottom: 24px;\n        }\n        \n        .category-title {\n          font-size: 14px;\n          font-weight: 600;\n          color: #495057;\n          margin-bottom: 12px;\n          text-transform: uppercase;\n          letter-spacing: 0.5px;\n        }\n        \n        .component-list {\n          display: flex;\n          flex-direction: column;\n          gap: 8px;\n        }\n        \n        .component-item {\n          padding: 12px 16px;\n          border: 1px solid #e1e5e9;\n          border-radius: 6px;\n          background: #f8f9fa;\n          cursor: grab;\n          transition: all 0.2s ease;\n          display: flex;\n          align-items: center;\n          gap: 12px;\n        }\n        \n        .component-item:hover {\n          background: #e9ecef;\n          border-color: #adb5bd;\n          transform: translateY(-1px);\n          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n        }\n        \n        .component-item:active,\n        .component-item.dragging {\n          cursor: grabbing;\n          transform: translateY(0);\n        }\n        \n        .component-icon {\n          width: 20px;\n          height: 20px;\n          color: #6c757d;\n          flex-shrink: 0;\n        }\n        \n        .component-info {\n          flex: 1;\n          min-width: 0;\n        }\n        \n        .component-name {\n          font-size: 14px;\n          font-weight: 500;\n          color: #495057;\n          margin-bottom: 2px;\n        }\n        \n        .component-description {\n          font-size: 12px;\n          color: #6c757d;\n          line-height: 1.3;\n        }\n        \n        .sidebar-footer {\n          padding: 16px 20px;\n          border-top: 1px solid #e1e5e9;\n          background: #f8f9fa;\n        }\n        \n        .help-text {\n          font-size: 12px;\n          color: #6c757d;\n          line-height: 1.4;\n        }\n        \n        .help-text p {\n          margin: 0 0 6px 0;\n        }\n        \n        .help-text p:last-child {\n          margin-bottom: 0;\n        }\n        \n        .help-text kbd {\n          background: #e9ecef;\n          border: 1px solid #adb5bd;\n          border-radius: 3px;\n          padding: 1px 4px;\n          font-size: 11px;\n          font-family: monospace;\n        }\n        \n        /* Responsive */\n        @media (max-width: 768px) {\n          .component-sidebar {\n            width: 100%;\n            height: 200px;\n            border-right: none;\n            border-bottom: 1px solid #e1e5e9;\n          }\n          \n          .sidebar-content {\n            padding: 12px;\n          }\n          \n          .component-list {\n            flex-direction: row;\n            overflow-x: auto;\n            gap: 12px;\n          }\n          \n          .component-item {\n            min-width: 150px;\n            flex-shrink: 0;\n          }\n          \n          .sidebar-footer {\n            display: none;\n          }\n        }\n      "})]})};n(844);Object.create(null);const yr={};function br(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];"string"===typeof t[0]&&yr[t[0]]||("string"===typeof t[0]&&(yr[t[0]]=new Date),function(){if(console&&console.warn){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];"string"===typeof t[0]&&(t[0]="react-i18next:: ".concat(t[0])),console.warn(...t)}}(...t))}const xr=(e,t)=>()=>{if(e.isInitialized)t();else{const n=()=>{setTimeout((()=>{e.off("initialized",n)}),0),t()};e.on("initialized",n)}};function wr(e,t,n){e.loadNamespaces(t,xr(e,n))}function Sr(e,t,n,r){"string"===typeof n&&(n=[n]),n.forEach((t=>{e.options.ns.indexOf(t)<0&&e.options.ns.push(t)})),e.loadLanguages(t,xr(e,r))}const kr=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,Cr={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"\xa9","&#169;":"\xa9","&reg;":"\xae","&#174;":"\xae","&hellip;":"\u2026","&#8230;":"\u2026","&#x2F;":"/","&#47;":"/"},Er=e=>Cr[e];let Tr={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:e=>e.replace(kr,Er)};let Dr;const Or=(0,t.createContext)();class jr{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach((e=>{this.usedNamespaces[e]||(this.usedNamespaces[e]=!0)}))}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}function Nr(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{i18n:r}=n,{i18n:a,defaultNS:o}=(0,t.useContext)(Or)||{},i=r||a||Dr;if(i&&!i.reportNamespaces&&(i.reportNamespaces=new jr),!i){br("You will need to pass in an i18next instance by using initReactI18next");const e=(e,t)=>"string"===typeof t?t:t&&"object"===typeof t&&"string"===typeof t.defaultValue?t.defaultValue:Array.isArray(e)?e[e.length-1]:e,t=[e,{},!1];return t.t=e,t.i18n={},t.ready=!1,t}i.options.react&&void 0!==i.options.react.wait&&br("It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const l=s(s(s({},Tr),i.options.react),n),{useSuspense:c,keyPrefix:u}=l;let d=e||o||i.options&&i.options.defaultNS;d="string"===typeof d?[d]:d||["translation"],i.reportNamespaces.addUsedNamespaces&&i.reportNamespaces.addUsedNamespaces(d);const f=(i.isInitialized||i.initializedStoreOnce)&&d.every((e=>function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return t.languages&&t.languages.length?void 0!==t.options.ignoreJSONStructure?t.hasLoadedNamespace(e,{lng:n.lng,precheck:(t,r)=>{if(n.bindI18n&&n.bindI18n.indexOf("languageChanging")>-1&&t.services.backendConnector.backend&&t.isLanguageChangingTo&&!r(t.isLanguageChangingTo,e))return!1}}):function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const r=t.languages[0],a=!!t.options&&t.options.fallbackLng,o=t.languages[t.languages.length-1];if("cimode"===r.toLowerCase())return!0;const i=(e,n)=>{const r=t.services.backendConnector.state["".concat(e,"|").concat(n)];return-1===r||2===r};return!(n.bindI18n&&n.bindI18n.indexOf("languageChanging")>-1&&t.services.backendConnector.backend&&t.isLanguageChangingTo&&!i(t.isLanguageChangingTo,e))&&(!!t.hasResourceBundle(r,e)||!(t.services.backendConnector.backend&&(!t.options.resources||t.options.partialBundledLanguages))||!(!i(r,e)||a&&!i(o,e)))}(e,t,n):(br("i18n.languages were undefined or empty",t.languages),!0)}(e,i,l)));function p(){return i.getFixedT(n.lng||null,"fallback"===l.nsMode?d:d[0],u)}const[g,h]=(0,t.useState)(p);let m=d.join();n.lng&&(m="".concat(n.lng).concat(m));const v=((e,n)=>{const r=(0,t.useRef)();return(0,t.useEffect)((()=>{r.current=n?r.current:e}),[e,n]),r.current})(m),y=(0,t.useRef)(!0);(0,t.useEffect)((()=>{const{bindI18n:e,bindI18nStore:t}=l;function r(){y.current&&h(p)}return y.current=!0,f||c||(n.lng?Sr(i,n.lng,d,(()=>{y.current&&h(p)})):wr(i,d,(()=>{y.current&&h(p)}))),f&&v&&v!==m&&y.current&&h(p),e&&i&&i.on(e,r),t&&i&&i.store.on(t,r),()=>{y.current=!1,e&&i&&e.split(" ").forEach((e=>i.off(e,r))),t&&i&&t.split(" ").forEach((e=>i.store.off(e,r)))}}),[i,m]);const b=(0,t.useRef)(!0);(0,t.useEffect)((()=>{y.current&&!b.current&&h(p),b.current=!1}),[i,u]);const x=[g,i,f];if(x.t=g,x.i18n=i,x.ready=f,f)return x;if(!f&&!c)return x;throw new Promise((e=>{n.lng?Sr(i,n.lng,d,(()=>e())):wr(i,d,(()=>e()))}))}const Ir=(0,t.createContext)(),_r=(0,t.createContext)(),Pr=e=>{let{selectedElements:n,canvasState:r}=e;const{t:a}=Nr(),[o,i]=(0,t.useState)(!1),[l,s]=(0,t.useState)("suggestions"),[c,u]=(0,t.useState)(null),{isConnected:d,suggestions:f,generateSuggestions:p,analyzeGoal:g,optimizeCanvas:h,checkConnection:m,clearSuggestions:v,error:b}=(()=>{const e=(0,t.useContext)(Ir);if(!e)throw new Error("usePAIM must be used within a PAIMProvider");return e})(),{setLoading:x,isLoading:w}=(()=>{const e=(0,t.useContext)(_r);if(!e)throw new Error("useLoading must be used within a LoadingProvider");return e})();(0,t.useEffect)((()=>{m()}),[m]);const S=n.map((e=>r.elements.find((t=>t.id===e)))).filter((e=>e&&"smart-goal"===e.type));return(0,y.jsxs)(y.Fragment,{children:[(0,y.jsxs)("button",{className:"paim-fab ".concat(o?"active":""),onClick:()=>i(!o),title:a("paimAssistant"),children:[(0,y.jsx)("div",{className:"fab-icon",children:(0,y.jsx)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor",children:(0,y.jsx)("path",{d:"M12,2A2,2 0 0,1 14,4C14,4.74 13.6,5.39 13,5.73V7A1,1 0 0,0 14,8H18A1,1 0 0,0 19,7V5.73C18.4,5.39 18,4.74 18,4A2,2 0 0,1 20,2A2,2 0 0,1 22,4C22,5.11 21.1,6 20,6V8A3,3 0 0,1 17,11H15V12H17A3,3 0 0,1 20,15V17C21.1,17 22,17.89 22,19A2,2 0 0,1 20,21A2,2 0 0,1 18,19C18,18.26 18.4,17.61 19,17.27V15A1,1 0 0,0 18,14H14A1,1 0 0,0 13,15V17.27C13.6,17.61 14,18.26 14,19A2,2 0 0,1 12,21A2,2 0 0,1 10,19C10,18.26 10.4,17.61 11,17.27V15A1,1 0 0,0 10,14H6A1,1 0 0,0 5,15V17.27C5.6,17.61 6,18.26 6,19A2,2 0 0,1 4,21A2,2 0 0,1 2,19C2,17.89 2.9,17 4,17V15A3,3 0 0,1 7,12H9V11H7A3,3 0 0,1 4,8V6C2.9,6 2,5.11 2,4A2,2 0 0,1 4,2A2,2 0 0,1 6,4C6,4.74 5.6,5.39 5,5.73V7A1,1 0 0,0 6,8H10A1,1 0 0,0 11,7V5.73C10.4,5.39 10,4.74 10,4A2,2 0 0,1 12,2Z"})})}),!d&&(0,y.jsx)("div",{className:"connection-indicator offline"})]}),o&&(0,y.jsxs)("div",{className:"paim-panel",children:[(0,y.jsxs)("div",{className:"panel-header",children:[(0,y.jsx)("h3",{children:a("paimAssistant")}),(0,y.jsx)("button",{className:"close-button",onClick:()=>i(!1),children:"\xd7"})]}),(0,y.jsxs)("div",{className:"panel-tabs",children:[(0,y.jsx)("button",{className:"tab ".concat("suggestions"===l?"active":""),onClick:()=>s("suggestions"),children:a("suggestions")}),(0,y.jsx)("button",{className:"tab ".concat("analysis"===l?"active":""),onClick:()=>s("analysis"),disabled:0===S.length,children:a("analysis")})]}),(0,y.jsxs)("div",{className:"panel-content",children:[!d&&(0,y.jsxs)("div",{className:"connection-warning",children:[(0,y.jsx)("p",{children:a("paimNotConnected")}),(0,y.jsx)("button",{onClick:m,children:a("reconnect")})]}),b&&(0,y.jsx)("div",{className:"error-message",children:(0,y.jsx)("p",{children:b})}),"suggestions"===l&&(0,y.jsxs)("div",{className:"suggestions-tab",children:[(0,y.jsxs)("div",{className:"tab-actions",children:[(0,y.jsx)("button",{className:"action-button primary",onClick:async()=>{if(!d)return void await m();x("suggestions",!0,"Generating suggestions...");const e={selectedElements:n.map((e=>r.elements.find((t=>t.id===e)))).filter(Boolean),canvasElements:r.elements,connections:r.connections};await p(e),x("suggestions",!1)},disabled:!d||w("suggestions"),children:w("suggestions")?a("loading"):a("generateSuggestions")}),(0,y.jsx)("button",{className:"action-button",onClick:async()=>{if(!d)return void await m();x("optimization",!0,"Optimizing canvas layout...");const e=await h(r);console.log("Canvas optimizations:",e),x("optimization",!1)},disabled:!d||w("optimization")||0===r.elements.length,children:w("optimization")?a("loading"):a("optimizeCanvas")}),f.length>0&&(0,y.jsx)("button",{className:"action-button secondary",onClick:v,children:a("clear")})]}),(0,y.jsx)("div",{className:"suggestions-list",children:0===f.length?(0,y.jsx)("p",{className:"empty-state",children:a("noSuggestions")}):f.map(((e,t)=>(0,y.jsxs)("div",{className:"suggestion-item",children:[(0,y.jsx)("div",{className:"suggestion-type",children:e.type}),(0,y.jsx)("div",{className:"suggestion-text",children:e.text}),e.action&&(0,y.jsx)("button",{className:"suggestion-action",onClick:()=>console.log("Apply suggestion:",e),children:a("apply")})]},t)))})]}),"analysis"===l&&(0,y.jsxs)("div",{className:"analysis-tab",children:[(0,y.jsx)("div",{className:"tab-actions",children:(0,y.jsx)("button",{className:"action-button primary",onClick:async()=>{if(1!==n.length)return;const e=r.elements.find((e=>e.id===n[0]));if(!e||"smart-goal"!==e.type)return;x("analysis",!0,"Analyzing SMART goal...");const t=await g(e.data);u(t),s("analysis"),x("analysis",!1)},disabled:!d||1!==S.length||w("analysis"),children:w("analysis")?a("loading"):a("analyzeGoal")})}),0===S.length&&(0,y.jsx)("p",{className:"empty-state",children:a("selectSmartGoal")}),S.length>1&&(0,y.jsx)("p",{className:"empty-state",children:a("selectSingleGoal")}),c&&(0,y.jsxs)("div",{className:"analysis-result",children:[(0,y.jsx)("h4",{children:a("analysisResult")}),(0,y.jsxs)("div",{className:"analysis-content",children:[c.score&&(0,y.jsxs)("div",{className:"score-section",children:[(0,y.jsx)("span",{className:"score-label",children:a("smartScore")}),(0,y.jsxs)("span",{className:"score-value",children:[c.score,"/100"]})]}),c.recommendations&&(0,y.jsxs)("div",{className:"recommendations",children:[(0,y.jsx)("h5",{children:a("recommendations")}),(0,y.jsx)("ul",{children:c.recommendations.map(((e,t)=>(0,y.jsx)("li",{children:e},t)))})]})]})]})]})]})]}),(0,y.jsx)("style",{jsx:!0,children:"\n        .paim-fab {\n          position: fixed;\n          bottom: 24px;\n          right: 24px;\n          width: 56px;\n          height: 56px;\n          border-radius: 50%;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          border: none;\n          color: white;\n          cursor: pointer;\n          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          transition: all 0.3s ease;\n          z-index: 1000;\n          position: relative;\n        }\n        \n        .paim-fab:hover {\n          transform: scale(1.1);\n          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);\n        }\n        \n        .paim-fab.active {\n          background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);\n        }\n        \n        .fab-icon {\n          transition: transform 0.3s ease;\n        }\n        \n        .paim-fab.active .fab-icon {\n          transform: rotate(180deg);\n        }\n        \n        .connection-indicator {\n          position: absolute;\n          top: 4px;\n          right: 4px;\n          width: 12px;\n          height: 12px;\n          border-radius: 50%;\n          background: #28a745;\n        }\n        \n        .connection-indicator.offline {\n          background: #dc3545;\n        }\n        \n        .paim-panel {\n          position: fixed;\n          bottom: 100px;\n          right: 24px;\n          width: 400px;\n          max-height: 600px;\n          background: white;\n          border-radius: 12px;\n          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);\n          z-index: 999;\n          display: flex;\n          flex-direction: column;\n          overflow: hidden;\n        }\n        \n        .panel-header {\n          padding: 16px 20px;\n          border-bottom: 1px solid #e1e5e9;\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          color: white;\n        }\n        \n        .panel-header h3 {\n          margin: 0;\n          font-size: 16px;\n          font-weight: 600;\n        }\n        \n        .close-button {\n          background: none;\n          border: none;\n          color: white;\n          font-size: 24px;\n          cursor: pointer;\n          padding: 0;\n          width: 24px;\n          height: 24px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        }\n        \n        .panel-tabs {\n          display: flex;\n          background: #f8f9fa;\n          border-bottom: 1px solid #e1e5e9;\n        }\n        \n        .tab {\n          flex: 1;\n          padding: 12px 16px;\n          background: none;\n          border: none;\n          cursor: pointer;\n          font-size: 14px;\n          color: #6c757d;\n          transition: all 0.2s ease;\n        }\n        \n        .tab:hover {\n          background: #e9ecef;\n        }\n        \n        .tab.active {\n          background: white;\n          color: #495057;\n          font-weight: 500;\n        }\n        \n        .tab:disabled {\n          opacity: 0.5;\n          cursor: not-allowed;\n        }\n        \n        .panel-content {\n          flex: 1;\n          overflow-y: auto;\n          padding: 20px;\n        }\n        \n        .connection-warning,\n        .error-message {\n          padding: 12px;\n          border-radius: 6px;\n          margin-bottom: 16px;\n        }\n        \n        .connection-warning {\n          background: #fff3cd;\n          border: 1px solid #ffeaa7;\n          color: #856404;\n        }\n        \n        .error-message {\n          background: #f8d7da;\n          border: 1px solid #f5c6cb;\n          color: #721c24;\n        }\n        \n        .tab-actions {\n          display: flex;\n          flex-direction: column;\n          gap: 8px;\n          margin-bottom: 20px;\n        }\n        \n        .action-button {\n          padding: 10px 16px;\n          border: 1px solid #dee2e6;\n          border-radius: 6px;\n          background: white;\n          cursor: pointer;\n          font-size: 14px;\n          transition: all 0.2s ease;\n        }\n        \n        .action-button:hover {\n          background: #f8f9fa;\n        }\n        \n        .action-button.primary {\n          background: #007bff;\n          color: white;\n          border-color: #007bff;\n        }\n        \n        .action-button.primary:hover {\n          background: #0056b3;\n        }\n        \n        .action-button.secondary {\n          background: #6c757d;\n          color: white;\n          border-color: #6c757d;\n        }\n        \n        .action-button:disabled {\n          opacity: 0.5;\n          cursor: not-allowed;\n        }\n        \n        .empty-state {\n          text-align: center;\n          color: #6c757d;\n          font-style: italic;\n          margin: 40px 0;\n        }\n        \n        .suggestions-list {\n          display: flex;\n          flex-direction: column;\n          gap: 12px;\n        }\n        \n        .suggestion-item {\n          padding: 12px;\n          border: 1px solid #e1e5e9;\n          border-radius: 6px;\n          background: #f8f9fa;\n        }\n        \n        .suggestion-type {\n          font-size: 12px;\n          color: #007bff;\n          font-weight: 500;\n          text-transform: uppercase;\n          margin-bottom: 4px;\n        }\n        \n        .suggestion-text {\n          font-size: 14px;\n          color: #495057;\n          margin-bottom: 8px;\n        }\n        \n        .suggestion-action {\n          background: #28a745;\n          color: white;\n          border: none;\n          padding: 4px 12px;\n          border-radius: 4px;\n          font-size: 12px;\n          cursor: pointer;\n        }\n        \n        .analysis-result {\n          border: 1px solid #e1e5e9;\n          border-radius: 6px;\n          padding: 16px;\n          background: #f8f9fa;\n        }\n        \n        .analysis-result h4 {\n          margin: 0 0 12px 0;\n          font-size: 14px;\n          color: #495057;\n        }\n        \n        .score-section {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 16px;\n          padding: 8px 12px;\n          background: white;\n          border-radius: 4px;\n        }\n        \n        .score-label {\n          font-size: 14px;\n          color: #6c757d;\n        }\n        \n        .score-value {\n          font-size: 18px;\n          font-weight: 600;\n          color: #28a745;\n        }\n        \n        .recommendations h5 {\n          margin: 0 0 8px 0;\n          font-size: 13px;\n          color: #495057;\n        }\n        \n        .recommendations ul {\n          margin: 0;\n          padding-left: 16px;\n        }\n        \n        .recommendations li {\n          font-size: 13px;\n          color: #6c757d;\n          margin-bottom: 4px;\n        }\n      "})]})};function zr(e){switch(e){case"smart-goal":return{title:"New SMART Goal",specific:"",measurable:"",achievable:"",relevant:"",timeBound:{startDate:null,endDate:null},progress:0};case"task":return{title:"New Task",description:"",dueDate:null,priority:"medium",status:"not-started",assignee:"",isRecurring:!1};case"kpi":return{title:"New KPI",metricType:"percentage",currentValue:0,targetValue:100,unit:"%",visualization:"progress-bar"};case"brand-asset":return{title:"New Brand Asset",assetType:"logo",fileUrl:null,tags:[]};default:return{}}}const Fr=()=>{const e=(0,t.useRef)(null),[n,r]=(0,t.useState)([]),[a,o]=(0,t.useState)(!1),{elements:i,connections:l,viewport:s,addElement:c,updateElement:u,removeElement:d,updateViewport:f,connectElements:p,disconnectElements:g}=Hn(),h=(0,t.useCallback)(((e,t)=>{const n={id:"".concat(e,"-").concat(Date.now()),type:e,position:t,data:zr(e),createdAt:(new Date).toISOString()};c(n)}),[c]),m=(0,t.useCallback)((function(e){r(arguments.length>1&&void 0!==arguments[1]&&arguments[1]?t=>t.includes(e)?t.filter((t=>t!==e)):[...t,e]:[e])}),[]),v=(0,t.useCallback)(((e,t)=>{u(e,{position:t})}),[u]),b=(0,t.useCallback)((t=>{t.target===e.current&&r([])}),[]),x=(0,t.useCallback)((e=>{if("Delete"===e.key&&n.length>0&&(n.forEach((e=>d(e))),r([])),e.ctrlKey||e.metaKey)switch(e.key){case"a":e.preventDefault(),r(i.map((e=>e.id)));break;case"z":case"y":e.preventDefault()}}),[n,i,d]);return(0,y.jsx)(vt,{backend:At,children:(0,y.jsxs)("div",{className:"smart-canvas-container",onKeyDown:x,tabIndex:0,role:"application","aria-label":"SMART Canvas Workspace",children:[(0,y.jsx)(vr,{onElementDrop:h}),(0,y.jsxs)("div",{className:"canvas-main",children:[(0,y.jsx)("div",{className:"context-bar-placeholder",children:(0,y.jsxs)("div",{className:"canvas-info",children:[(0,y.jsxs)("span",{children:["Elements: ",i.length]}),(0,y.jsxs)("span",{children:["Selected: ",n.length]}),(0,y.jsxs)("span",{children:["Zoom: ",Math.round(100*s.zoom),"%"]})]})}),(0,y.jsxs)("div",{ref:e,className:"canvas-workspace",onClick:b,role:"main","aria-label":"Canvas workspace for SMART goals and tasks",children:[(0,y.jsx)(ir,{elements:i,selectedElements:n,onElementSelect:m,onElementMove:v,onElementDrop:h,viewport:s,onViewportChange:f}),(0,y.jsx)("div",{className:"connections-placeholder",children:l.length>0&&(0,y.jsxs)("div",{className:"connection-info",children:[l.length," connection(s)"]})})]})]}),(0,y.jsx)(Pr,{selectedElements:n,canvasState:{elements:i,connections:l}})]})})},Lr=()=>{const{language:e,switchLanguage:t}=k();return(0,y.jsx)("nav",{style:{padding:"1rem 0",borderBottom:"1px solid var(--cove-color-border-light)",marginBottom:"2rem"},children:(0,y.jsx)(V,{size:"xl",children:(0,y.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",flexWrap:"wrap",gap:"1rem"},children:[(0,y.jsx)(A,{variant:"h3",weight:"bold",children:"ar"===e?"\u0643\u0648\u0641 - \u0645\u0646\u0635\u0629 \u0627\u0644\u0630\u0643\u0627\u0621 \u0627\u0644\u0627\u0635\u0637\u0646\u0627\u0639\u064a":"Cove - AI Platform"}),(0,y.jsxs)("div",{style:{display:"flex",gap:"1rem",alignItems:"center"},children:[(0,y.jsx)(F,{variant:"en"===e?"primary":"secondary",size:"sm",onClick:()=>t("en"),children:"English"}),(0,y.jsx)(F,{variant:"ar"===e?"primary":"secondary",size:"sm",onClick:()=>t("ar"),children:"\u0627\u0644\u0639\u0631\u0628\u064a\u0629"})]})]})})})},Rr=()=>{const[e,n]=(0,t.useState)("design-system"),{language:r}=k(),a={"design-system":{title:"ar"===r?"\u0646\u0638\u0627\u0645 \u0627\u0644\u062a\u0635\u0645\u064a\u0645":"Design System",component:(0,y.jsx)(ae,{})},"smart-canvas":{title:"ar"===r?"\u0627\u0644\u0644\u0648\u062d\u0629 \u0627\u0644\u0630\u0643\u064a\u0629":"Smart Canvas",component:(0,y.jsx)(Fr,{})}};return(0,y.jsxs)("div",{style:{minHeight:"100vh",backgroundColor:"var(--cove-color-background-light)"},children:[(0,y.jsx)(Lr,{}),(0,y.jsxs)(V,{size:"xl",padding:"lg",children:[(0,y.jsxs)("div",{style:{marginBottom:"2rem"},children:[(0,y.jsx)(A,{variant:"h4",style:{marginBottom:"1rem"},children:"ar"===r?"\u0627\u062e\u062a\u0631 \u0627\u0644\u0639\u0631\u0636:":"Select View:"}),(0,y.jsx)("div",{style:{display:"flex",gap:"1rem",flexWrap:"wrap"},children:Object.entries(a).map((t=>{let[r,a]=t;return(0,y.jsx)(F,{variant:e===r?"primary":"secondary",onClick:()=>n(r),children:a.title},r)}))})]}),(0,y.jsx)("div",{children:a[e].component}),"design-system"===e&&(0,y.jsxs)(V,{padding:"lg",style:{marginTop:"3rem",backgroundColor:"var(--cove-color-background-elevated)",borderRadius:"8px",border:"1px solid var(--cove-color-border-light)"},children:[(0,y.jsx)(A,{variant:"h3",style:{marginBottom:"1.5rem"},children:"ar"===r?"\u062d\u0627\u0644\u0629 \u0627\u0644\u062a\u0646\u0641\u064a\u0630 - \u0627\u0644\u0645\u0631\u062d\u0644\u0629 1.1":"Implementation Status - Phase 1.1"}),(0,y.jsxs)("div",{style:{display:"grid",gap:"1rem"},children:[(0,y.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"0.5rem"},children:[(0,y.jsx)("span",{style:{color:"var(--cove-color-success-default)",fontSize:"1.2em"},children:"\u2705"}),(0,y.jsx)(A,{variant:"body",children:"ar"===r?"\u0646\u0638\u0627\u0645 \u0627\u0644\u0631\u0645\u0648\u0632 \u0627\u0644\u0645\u0645\u064a\u0632\u0629 \u0644\u0644\u062a\u0635\u0645\u064a\u0645 \u0645\u0639 \u0627\u0644\u062a\u0643\u064a\u0641 \u0627\u0644\u062b\u0642\u0627\u0641\u064a":"Design Token System with Cultural Adaptation"})]}),(0,y.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"0.5rem"},children:[(0,y.jsx)("span",{style:{color:"var(--cove-color-success-default)",fontSize:"1.2em"},children:"\u2705"}),(0,y.jsx)(A,{variant:"body",children:"ar"===r?"\u0645\u0643\u062a\u0628\u0629 \u0627\u0644\u0645\u0643\u0648\u0646\u0627\u062a \u0645\u0639 \u0625\u0645\u0643\u0627\u0646\u064a\u0629 \u0627\u0644\u0648\u0635\u0648\u0644 \u0627\u0644\u0643\u0627\u0645\u0644\u0629 (WCAG 2.1 AA)":"Component Library with Full Accessibility (WCAG 2.1 AA)"})]}),(0,y.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"0.5rem"},children:[(0,y.jsx)("span",{style:{color:"var(--cove-color-success-default)",fontSize:"1.2em"},children:"\u2705"}),(0,y.jsx)(A,{variant:"body",children:"ar"===r?"\u0646\u0638\u0627\u0645 \u0627\u0644\u062a\u062e\u0637\u064a\u0637 \u0645\u0646 \u0627\u0644\u064a\u0645\u064a\u0646 \u0644\u0644\u064a\u0633\u0627\u0631 \u0645\u0639 \u0627\u0644\u0637\u0628\u0627\u0639\u0629 \u0627\u0644\u0639\u0631\u0628\u064a\u0629":"RTL Layout System with Arabic Typography"})]}),(0,y.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"0.5rem"},children:[(0,y.jsx)("span",{style:{color:"var(--cove-color-success-default)",fontSize:"1.2em"},children:"\u2705"}),(0,y.jsx)(A,{variant:"body",children:"ar"===r?"\u0646\u0638\u0627\u0645 \u0627\u0644\u0623\u0644\u0648\u0627\u0646 \u0648\u0627\u0644\u0633\u0645\u0627\u062a \u0645\u0639 \u0646\u0633\u0628 \u0627\u0644\u062a\u0628\u0627\u064a\u0646 AAA":"Color & Theme System with AAA Contrast Ratios"})]}),(0,y.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"0.5rem"},children:[(0,y.jsx)("span",{style:{color:"var(--cove-color-success-default)",fontSize:"1.2em"},children:"\u2705"}),(0,y.jsx)(A,{variant:"body",children:"ar"===r?"\u062a\u062d\u0633\u064a\u0646 \u0627\u0644\u0637\u0628\u0627\u0639\u0629 \u0648\u0627\u0644\u062e\u0637\u0648\u0637 (<200ms \u062a\u062d\u0645\u064a\u0644)":"Typography & Font Optimization (<200ms loading)"})]}),(0,y.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"0.5rem"},children:[(0,y.jsx)("span",{style:{color:"var(--cove-color-success-default)",fontSize:"1.2em"},children:"\u2705"}),(0,y.jsx)(A,{variant:"body",children:"ar"===r?"\u0627\u062e\u062a\u0628\u0627\u0631 \u0625\u0645\u0643\u0627\u0646\u064a\u0629 \u0627\u0644\u0648\u0635\u0648\u0644 \u0627\u0644\u0622\u0644\u064a \u0645\u0639 axe-core":"Automated Accessibility Testing with axe-core"})]})]}),(0,y.jsxs)("div",{style:{marginTop:"1.5rem",padding:"1rem",backgroundColor:"var(--cove-color-background-paper)",borderRadius:"4px"},children:[(0,y.jsx)(A,{variant:"bodySmall",weight:"medium",style:{marginBottom:"0.5rem"},children:"ar"===r?"\u0627\u0644\u0645\u064a\u0632\u0627\u062a \u0627\u0644\u0631\u0626\u064a\u0633\u064a\u0629:":"Key Features:"}),(0,y.jsx)(A,{variant:"bodySmall",children:"ar"===r?"\u2022 \u062f\u0639\u0645 \u0643\u0627\u0645\u0644 \u0644\u0644\u063a\u0629 \u0627\u0644\u0639\u0631\u0628\u064a\u0629 \u0648\u0627\u0644\u062a\u062e\u0637\u064a\u0637 \u0645\u0646 \u0627\u0644\u064a\u0645\u064a\u0646 \u0644\u0644\u064a\u0633\u0627\u0631 \u2022 \u0645\u062a\u0648\u0627\u0641\u0642 \u0645\u0639 WCAG 2.1 AA/AAA \u2022 \u062a\u0643\u064a\u0641 \u062b\u0642\u0627\u0641\u064a \u0644\u0645\u0646\u0637\u0642\u0629 \u0627\u0644\u062e\u0644\u064a\u062c \u2022 \u0623\u062f\u0627\u0621 \u0645\u062d\u0633\u0646 (<5% \u0625\u0636\u0627\u0641\u0629) \u2022 \u062f\u0639\u0645 \u0627\u0644\u062d\u0631\u0643\u0629 \u0627\u0644\u0645\u062d\u062a\u0631\u0645\u0629 \u2022 \u062e\u0637\u0648\u0637 \u0645\u0646\u0627\u0633\u0628\u0629 \u0644\u0639\u0633\u0631 \u0627\u0644\u0642\u0631\u0627\u0621\u0629":"\u2022 Full Arabic & RTL Support \u2022 WCAG 2.1 AA/AAA Compliant \u2022 Gulf Region Cultural Adaptation \u2022 Performance Optimized (<5% overhead) \u2022 Respectful Motion Support \u2022 Dyslexia-Friendly Fonts"})]})]})]})]})};const Mr=function(){return(0,y.jsx)(T,{defaultTheme:"auto",defaultLanguage:"en",children:(0,y.jsx)(Rr,{})})};r.createRoot(document.getElementById("root")).render((0,y.jsx)(t.StrictMode,{children:(0,y.jsx)(Mr,{})}))})()})();
//# sourceMappingURL=main.474a3b17.js.map